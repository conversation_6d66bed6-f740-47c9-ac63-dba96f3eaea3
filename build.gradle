allprojects {
    apply plugin: 'java'
    apply plugin: 'java-library'

    repositories {
//        jcenter()
        mavenCentral()
    }

    compileJava {
        options.encoding = 'UTF-8'
    }

    tasks.withType(JavaCompile) {
        options.warnings = false
    }

    compileTestJava {
        options.encoding = 'UTF-8'
    }

    dependencies {
        repositories {
            mavenLocal()
            mavenCentral()
//            maven {
//                url 'https://maven.aliyun.com/nexus/content/groups/public/'
//            }
//            maven {
//                url 'http://nexus.capvision.com/repository/ks_server'
//            }
            maven {
                url 'https://nexus-us.capvision.com/repository/us_server'
            }
//            maven {
//                url 'http://nexus.capvision.us:32080/repository/us_server'
//                allowInsecureProtocol = true
//            }
        }

        testImplementation 'junit:junit:4.11'
        testImplementation 'org.springframework:spring-test:4.1.3.RELEASE'
    }

    /* configurations {
         //编译期排除加入依赖的jar包
         api.exclude module: 'fastjson'
         api.exclude module: 'log4j'
         api.exclude module: 'httpmime'
         api.exclude module: 'httpcore'
         api.exclude module: 'httpclient'
         api.exclude module: 'commons-lang3'
         api.exclude module: 'commons-codec'
         api.exclude module: 'commons-logging'
     }*/
}

subprojects {
    configurations {
        // 其余目前log4j的漏洞只影响 log4j2 也就是log4j 2.x 目前我们没有使用到log4j 2.x 所以不影响，之修复log4j 1.2x的漏洞 http://blog.nsfocus.net/apache-log4j-3/
        api.exclude group: 'log4j', module: 'log4j'

    }
}

project(':mq_common') {
    dependencies {
        /** Network **/
        api 'com.sun.jersey:jersey-bundle:1.19'
        api 'com.mashape.unirest:unirest-java:1.4.9'

        /** Rpc Client **/
//        api 'com.cpvsn.rpc.service:rpc_client:1.0.5'
        api 'com.cpvsn.rpc.service:rpc_common:1.2.31'
        api 'com.cpvsn.core:core:1.1.29'
        api('com.alibaba:fastjson:1.2.47')
        api 'com.weibo:motan-core:0.2.0'
        api 'com.weibo:motan-transport-netty:0.2.0'
        api 'com.weibo:motan-springsupport:0.2.0'
        api 'com.weibo:motan-registry-zookeeper:0.2.0'

        /** Mq **/
        api 'org.springframework.amqp:spring-rabbit:1.4.1.RELEASE'

        /** Db **/
        api 'org.mybatis:mybatis:3.2.8'
        api 'org.mybatis:mybatis-spring:1.2.2'
        api 'mysql:mysql-connector-java:5.1.34'
        api 'commons-dbcp:commons-dbcp:1.4'

        api 'org.aeonbits.owner:owner:1.0.8'
        api 'com.cpvsn:log4j:1.2.17'

        api fileTree(dir: 'libs', include: '*.jar')
    }
}

project(':mq_main') {
    dependencies {
        /** Mail **/
        api 'javax.mail:mail:1.4.7'

        api 'commons-io:commons-io:2.4'
        api 'org.springframework:spring-context-support:4.1.3.RELEASE'
        api 'org.springframework:spring-aspects:4.1.3.RELEASE'
        api 'org.springframework:spring-jdbc:4.1.3.RELEASE'
        api 'org.apache.httpcomponents:fluent-hc:4.5.2'

        // sendgrid
        api 'com.sendgrid:sendgrid-java:4.9.1'

        api('com.cpvsn:msgraph-sdk-java:5.47.0')
        api 'com.microsoft.graph:microsoft-graph-core:2.0.16'
        api 'com.azure:azure-core-http-okhttp:1.11.6'
        api('com.azure:azure-identity:1.13.0') {
            exclude group: 'com.azure', module: 'azure-core-http-netty'
        }
        api 'com.azure:azure-core-http-okhttp:1.11.6'
        api 'org.apache.tika:tika-core:1.18'
        api 'commons-codec:commons-codec:1.6'

        /** Jpush **/
        api 'cn.jpush.api:jpush-client:3.2.7'

        /** redis **/
        api 'redis.clients:jedis:2.7.3'

        api fileTree(dir: 'libs', include: '*.jar')
        /** postmark **/
        api 'com.postmarkapp:postmark:1.11.1'
        api('org.slf4j:slf4j-api:1.7.30') {
            force = true
        }

        /** resend **/
        api 'com.resend:resend-java:3.1.0'

        /** mailgun **/
        implementation 'com.mailgun:mailgun-java:1.1.3'

        /** mailjet **/
        implementation 'com.mailjet:mailjet-client:5.2.5'

        /** google email api **/
        implementation 'com.google.api-client:google-api-client:1.25.0'
        implementation 'com.google.oauth-client:google-oauth-client:1.34.0'
        implementation 'com.google.apis:google-api-services-gmail:v1-rev110-1.25.0'
        implementation 'com.google.apis:google-api-services-calendar:v3-rev411-1.25.0'
        implementation 'com.google.apis:google-api-services-admin-directory:directory_v1-rev118-1.25.0'
        // Google OAuth2 客户端 (使用新版本的 GoogleCredentials)
        implementation 'com.google.auth:google-auth-library-oauth2-http:1.12.0'

        api fileTree(dir: 'libs', include: '*.jar')

        api project(':mq_common')
    }
}
