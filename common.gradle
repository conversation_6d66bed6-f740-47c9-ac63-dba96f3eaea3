jar {
    baseName = BASE_NAME
    version = VERSION
    excludes = ["properties", "mybatis", "services", "img"]
    manifest {
        attributes(
                "Class-Path": '. ' + configurations.runtimeClasspath.collect { 'lib/' + it.getName() }.join(' '),
                "Main-Class": MAIN_CLASS
        )
    }
}

task cleanBuild(type: Delete, dependsOn: [clean, jar]) {
    delete 'dist'
    delete 'release'
}

task copyJar(type: Copy, dependsOn: cleanBuild) {
    from 'build/libs'
    into 'dist'
}

task copyRelease(type: Copy) {
    from 'build/distributions'
    into 'release'
}

task tarDist(type: Zip) {
    from 'dist'
    archiveName BASE_NAME + '.' + extension

    finalizedBy copyRelease
}

task copyLibs(type: Copy) {
    from 'libs'
    from configurations.runtimeClasspath
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
    into 'dist/lib'
    finalizedBy tarDist
}

task copyDevResources(type: Copy, dependsOn: copyJar) {
    from 'src/main/resources'
    into 'dist'
}

task releaseDev(type: Copy, dependsOn: copyDevResources) {
    from 'src/main/resources'
    into 'dist'
    finalizedBy copyLibs
}

task releaseTest(type: Copy, dependsOn: copyDevResources) {
    from 'build_config/test'
    into 'dist'
    finalizedBy copyLibs
}

task releaseProdUs(type: Copy, dependsOn: copyDevResources) {
    from 'build_config/prod_us'
    into 'dist'
    finalizedBy copyLibs
}

task releaseProdSg(type: Copy, dependsOn: copyDevResources) {
    from 'build_config/prod_sg'
    into 'dist'
    finalizedBy copyLibs
}

