apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${JOB_NAME}-${BRANCH}
  namespace: ${NAMESPACE}
spec:
  replicas: ${REPLICAS}
  selector:
    matchLabels:
      app: ${JOB_NAME}-${BRANCH}
  template:
    metadata:
      labels:
        app: ${JOB_NAME}-${BRANCH}
    spec:
      containers:
        - name: ${JOB_NAME}-${BRANCH}
          image: repo.d.k8s/${JOB_NAME}-${BRANCH}:${BUILD_NUMBER}
          ports:
            - containerPort: 8002
      imagePullSecrets:
        - name: regcred
