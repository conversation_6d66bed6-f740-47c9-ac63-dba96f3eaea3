# MQ Handler

This repository contains a Java-based MQ message handler for processing and sending emails. It supports various mail sending services and handles different types of email-related messages.

## Project Structure

The project is divided into two main modules:

-   `mq_common`: This module contains common classes and interfaces, such as message listeners, data models, and utility classes.
-   `mq_main`: This is the main application module. It includes the message handlers, mail sending services, and the application's entry point.

## Core Functionalities

-   **Message Handling**: The application listens to a message queue (MQ) for incoming messages. The `MailHandler` class is the primary message handler, which processes different types of messages based on their `msg_type`.
-   **Email Sending**: The application supports sending emails through various services, including:
    -   Microsoft Exchange
    -   Microsoft Graph API
    -   Google API
    -   SendGrid
    -   Postmark
-   **Calendar Events**: The application can also handle calendar-related events, such as sending and canceling calendar invitations.
-   **Configuration**: The application's behavior can be configured through properties files, allowing for easy switching between different mail sending services and other settings.

## Workflow

1.  **Message Consumption**: The `Bootstrap` class initializes the Spring application context and starts the message listeners.
2.  **Message Handling**: The `MailHandler` receives messages from the queue and delegates them to the appropriate service based on the message type.
3.  **Email Service**: The `MailServiceImpl` class contains the core logic for sending emails and calendar events. It uses the configured mail sending service to deliver the messages.
4.  **Mail Senders**: The `ExchangeSender`, `MicrosoftGraphApiSender`, and `GoogleApiSender` classes implement the actual communication with the respective mail servers.

## MQ Message Routing Rules

The following outlines how MQ messages are processed and routed to different email sending services:

### Queues and Handlers

- **QUEUE_MAIL** (`com.cpvsn.mq.constant.QueueConst.QUEUE_MAIL`): handled by `MailHandler`
  - Processes messages based on `msg_type`:
    - **Mail**: standard email messages, routed via `MailService.sendMail(EmailModel)`
    - **calendar**: calendar invitations, routed via `MailService.sendCalendar(EmailModel)`
    - **cancel_calendar**: cancellation of calendar invitations, routed via `MailService.cancelCalendar(EmailModel)`
    - **batch_exchange**: batch exchange emails, routed via `MailHandler.sendMailViaExchange`
- **QUEUE_MAIL_BATCH** (`com.cpvsn.mq.constant.QueueConst.QUEUE_MAIL_BATCH`): handled by `MailBatchHandler`
  - Processes batch email messages via `MailService.sendBatchMail(BatchEmailModel)`

### Vendor Selection

In `MailService.sendMail`:
- Determines `prefer_vendor` from `EmailModel` or default (`mail_send_default_vendor` in config)
- Supported vendors:
  - `SEND_GRID` → `SendgridMailService`
  - `POSTMARK` → `PostMarkService`
  - `EXCHANGE` → Microsoft Exchange (Graph API or SMTP based on config)
  - `RESEND` → `ResendService`
  - `MAILGUN` → `MailgunService`
  - `MAILJET` → `MailjetService`

### Exchange vs. Graph API

- For **exchange** messages (`batch_exchange` and `EXCHANGE` vendor):
  - If `mail_send_type` config is `graph_api`: uses `MicrosoftGraphApiSender`
  - Else: uses `ExchangeSender.sendWithAtt`

### Calendar Handling

- **sendCalendar** / **cancelCalendar**:
  - Calendar messages bypass the generic vendor selection and use only calendar-specific APIs.
  - If `from` email ends with Gmail suffix (`MailConst.CAPVISION_PRO_GMAIL_SUFFIX`): uses `GoogleApiSender`
  - Else: uses `MicrosoftGraphApiSender` (Microsoft Graph API)

---

基于对代码的深入分析，这个MQ Handler项目的主要设计复杂度集中在以下几个核心部分：

## 1. **多邮件服务提供商的统一抽象和路由逻辑** (最主要的复杂度)

````java path=mq_main/src/main/java/com/cpvsn/mq/service/impl/MailServiceImpl.java mode=EXCERPT
EmailModel.Vendor vendor = getVendor(emailModel.getPrefer_vendor());
if (vendor == null) return false;

switch (vendor) {
    case SEND_GRID:
        return sendgridMailService.sendMail(emailModel);
    case POSTMARK:
        return postMarkService.sendMail(emailModel);
    case EXCHANGE:
        MicrosoftGraphApiSender.sendEmailWithAttachments(emailModel);
        break;
    case RESEND:
        resendService.sendEmail(emailModel);
        break;
    case MAILGUN:
        return mailgunService.sendMail(emailModel);
    case MAILJET:
        return mailjetService.sendMail(emailModel);
````

项目需要支持6+种不同的邮件服务提供商（SendGrid、Postmark、Exchange、Resend、Mailgun、Mailjet），每个提供商都有不同的API接口、认证方式和功能特性。

## 2. **消息类型路由和处理逻辑**

````java path=mq_main/src/main/java/com/cpvsn/mq/handler/MailHandler.java mode=EXCERPT
switch (msgType) {
    case MailServiceImpl.MSG_TYP_MAIL:
        EmailModel model = JSONObject.parseObject(dataObj.toJSONString(), EmailModel.class);
        mailService.sendMail(model);
        break;
    case MailServiceImpl.SEND_CALENDAR:
        mailService.sendCalendar(JSONObject.parseObject(dataObj.toJSONString(), EmailModel.class));
        break;
    case MailServiceImpl.CANCEL_CALENDAR:
        mailService.cancelCalendar(JSONObject.parseObject(dataObj.toJSONString(), EmailModel.class));
        break;
    case MailServiceImpl.BATCH_EXCHANGE:
        sendMailViaExchange(dataObj);
        break;
}
````

系统需要处理多种消息类型：普通邮件、日历邀请、日历取消、批量Exchange邮件等，每种类型都有不同的处理逻辑。

## 3. **配置驱动的服务选择机制**

````properties path=mq_main/src/main/resources/properties/conf.properties mode=EXCERPT
# 公司邮件代理发送邮件的方式 exchange 和 microsoft_graph_api
mail_send_type=microsoft_graph_api
# 发送邮件默认使用的供应商
mail_send_default_vendor=SEND_GRID
# outreachHandler使用的发送方式：email 公司邮箱代理发送 sendgrid 群发
outreach_mail_send_type=postmark
````

系统通过配置文件动态选择邮件发送方式，不同环境（开发、生产、不同地区）使用不同的配置，增加了配置管理的复杂性。

## 4. **日历事件的跨平台处理**

````java path=mq_main/src/main/java/com/cpvsn/mq/service/impl/MailServiceImpl.java mode=EXCERPT
public boolean sendCalendar(EmailModel emailModel) throws Exception {
    filterEmailModel(emailModel);

    if (StringUtils.endsWith(emailModel.getFrom(), MailConst.CAPVISION_PRO_GMAIL_SUFFIX)) {
        GoogleApiSender.sendCalendar(emailModel);
    } else {
        MicrosoftGraphApiSender.sendCalendar(emailModel);
    }

    return true;
}
````

日历功能需要根据发件人邮箱后缀自动选择Google API或Microsoft Graph API，涉及两套完全不同的API体系。

## 5. **Exchange邮件的双重发送机制**

````java path=mq_main/src/main/java/com/cpvsn/mq/handler/MailHandler.java mode=EXCERPT
if (MailConst.MAIL_SEND_TYPE_GRAPH_API.equals(mainConfig.mail_send_type())) {
    mailService.filterEmailModel(emailModel);
    MicrosoftGraphApiSender.sendEmailWithAttachments(emailModel);
} else {
    ExchangeSender.sendWithAtt(MailConst.SENDER_TYPE_COMMON, emailModel.getFrom(),
            emailModel.getFromName(),
            emailModel.getTo().toArray(new String[emailModel.getTo().size()]),
            // ... 更多参数
    );
}
````

对于Exchange邮件，系统支持两种发送方式：传统的Exchange SMTP和现代的Microsoft Graph API，需要根据配置动态切换。

## 6. **批量邮件处理的优化逻辑**

批量邮件处理需要考虑不同服务商的批量发送能力，有些支持真正的批量API（如Resend），有些只能循环单发，增加了处理逻辑的复杂性。

## 总结

这个项目的设计复杂度主要体现在**多服务商集成的抽象层设计**上，需要在统一的接口下兼容多种不同的邮件服务提供商，同时还要处理配置驱动的服务选择、跨平台的日历集成等问题。这种设计既保证了系统的灵活性和可扩展性，也带来了相当的实现复杂度。
