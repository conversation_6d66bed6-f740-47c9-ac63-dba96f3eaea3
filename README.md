# MQ Handler

This repository contains a Java-based MQ message handler for processing and sending emails. It supports various mail sending services and handles different types of email-related messages.

## Project Structure

The project is divided into two main modules:

-   `mq_common`: This module contains common classes and interfaces, such as message listeners, data models, and utility classes.
-   `mq_main`: This is the main application module. It includes the message handlers, mail sending services, and the application's entry point.

## Core Functionalities

-   **Message Handling**: The application listens to a message queue (MQ) for incoming messages. The `MailHandler` class is the primary message handler, which processes different types of messages based on their `msg_type`.
-   **Email Sending**: The application supports sending emails through various services, including:
    -   Microsoft Exchange
    -   Microsoft Graph API
    -   Google API
    -   SendGrid
    -   Postmark
-   **Calendar Events**: The application can also handle calendar-related events, such as sending and canceling calendar invitations.
-   **Configuration**: The application's behavior can be configured through properties files, allowing for easy switching between different mail sending services and other settings.

## Workflow

1.  **Message Consumption**: The `Bootstrap` class initializes the Spring application context and starts the message listeners.
2.  **Message Handling**: The `MailHandler` receives messages from the queue and delegates them to the appropriate service based on the message type.
3.  **Email Service**: The `MailServiceImpl` class contains the core logic for sending emails and calendar events. It uses the configured mail sending service to deliver the messages.
4.  **Mail Senders**: The `ExchangeSender`, `MicrosoftGraphApiSender`, and `GoogleApiSender` classes implement the actual communication with the respective mail servers.

## MQ Message Routing Rules

The following outlines how MQ messages are processed and routed to different email sending services:

### Queues and Handlers

- **QUEUE_MAIL** (`com.cpvsn.mq.constant.QueueConst.QUEUE_MAIL`): handled by `MailHandler`
  - Processes messages based on `msg_type`:
    - **Mail**: standard email messages, routed via `MailService.sendMail(EmailModel)`
    - **calendar**: calendar invitations, routed via `MailService.sendCalendar(EmailModel)`
    - **cancel_calendar**: cancellation of calendar invitations, routed via `MailService.cancelCalendar(EmailModel)`
    - **batch_exchange**: batch exchange emails, routed via `MailHandler.sendMailViaExchange`
- **QUEUE_MAIL_BATCH** (`com.cpvsn.mq.constant.QueueConst.QUEUE_MAIL_BATCH`): handled by `MailBatchHandler`
  - Processes batch email messages via `MailService.sendBatchMail(BatchEmailModel)`

### Vendor Selection

In `MailService.sendMail`:
- Determines `prefer_vendor` from `EmailModel` or default (`mail_send_default_vendor` in config)
- Supported vendors:
  - `SEND_GRID` → `SendgridMailService`
  - `POSTMARK` → `PostMarkService`
  - `EXCHANGE` → Microsoft Exchange (Graph API or SMTP based on config)
  - `RESEND` → `ResendService`
  - `MAILGUN` → `MailgunService`
  - `MAILJET` → `MailjetService`

### Exchange vs. Graph API

- For **exchange** messages (`batch_exchange` and `EXCHANGE` vendor):
  - If `mail_send_type` config is `graph_api`: uses `MicrosoftGraphApiSender`
  - Else: uses `ExchangeSender.sendWithAtt`

### Calendar Handling

- **sendCalendar** / **cancelCalendar**:
  - Calendar messages bypass the generic vendor selection and use only calendar-specific APIs.
  - If `from` email ends with Gmail suffix (`MailConst.CAPVISION_PRO_GMAIL_SUFFIX`): uses `GoogleApiSender`
  - Else: uses `MicrosoftGraphApiSender` (Microsoft Graph API)
