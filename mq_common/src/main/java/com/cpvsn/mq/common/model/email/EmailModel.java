package com.cpvsn.mq.common.model.email;

import org.apache.commons.lang3.StringUtils;
import org.omg.CORBA.UNKNOWN;

import java.io.Serializable;
import java.time.DayOfWeek;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 邮件模型
 * Created by cosx on 2015/11/24.
 */
public class EmailModel implements Serializable {

    private int sender_type;

    private String type;

    private String from;
    private String fromName;
    private List<String> to;
    /** 日历中的可选的参会人邮件地址 **/
    private List<String> optional_to;
    private List<String> cc;
    private List<String> bcc;
    private String subject;
    private String content;

    private List<EmailAttModel> attList;

    private Map<String, String> heads;

    /**
     * Calendar
     **/
    private String calendarUid;
    private int sequence;
    private Date startTime;
    private Date endTime;
    private String location;

    /**
     * 如果要让邮件显示在同一邮件链上（即类似在outlook邮件中直接回复转发的邮件可以自动折叠起来）则需要设置本次message id和 上一封邮件（原邮件)的id reply_message_id
     **/
    private String message_id;
    private String reply_message_id;
    private Map<String, Object> extra_params;
    private List<String> reply_to_list;

    /** Repeat Calendar **/
    private String repeatRule;
    private Date repeatEndTime;
    /** 重复日历的时间间隔 **/
    private int interval = 1;
    /** 重复日历相关设置 **/
    private int dayOfMonth;
    private List<DayOfWeek> daysOfWeek;
    private DayOfWeek firstDayOfWeek;
    private int month;
    private WeekIndex weekIndex;

    // 优先使用哪个供应商发送邮件
    private String prefer_vendor;


    /**
     * 重复日历事件的类型
     */
    public enum RepeatRule {
        /**
         * daily 事件按 interval 指定的时间间隔天数重复发生。
         */
        DAILY,
        /**
         * weekly 	事件按时间间隔周数在一周内的一天或几天重复发生。
         */
        WEEKLY,
        /**
         * absolute Monthly 事件按时间间隔月数在相应月份的指定一天（例如 15 号）重复发生。例如事件每季度（每 3 个月）的 15 号重复发生一次。
         */
        ABSOLUTE_MONTHLY,
        /**
         * relative Monthly 	事件按时间间隔月数在一周内的指定一天或几天（相应月份的同一相对位置）重复发生。例如事件每 3 个月的第二个星期四或星期五重复发生一次。
         */
        RELATIVE_MONTHLY,
        /**
         * absolute Yearly 事件按时间间隔年数在指定月份的一天重复发生。
         */
        ABSOLUTE_YEARLY,
        /**
         * relative Yearly 	事件按时间间隔年数在一周内的指定一天或几天（相应年份和月份的同一相对位置）重复发生。
         */
        RELATIVE_YEARLY,

    }

    /**
     * 重复日历中指定月的第几周发生
     */
    public enum WeekIndex {
        /**
         * first
         */
        FIRST,
        /**
         * second
         */
        SECOND,
        /**
         * third
         */
        THIRD,
        /**
         * fourth
         */
        FOURTH,
        /**
         * last
         */
        LAST,
    }

    /**
            * e.g.
        *
        * prefer_vendor = Vendor.SEND_GRID.name
     **/
    public enum Vendor {
        SEND_CLOUD,
        EXCHANGE,
        SEND_GRID,
        POSTMARK,
        RESEND,
        MAILGUN,
        MAILJET,
        UNKNOWN;
    }



    public int getSender_type() {
        return sender_type;
    }

    public void setSender_type(int sender_type) {
        this.sender_type = sender_type;
    }

    public int getSequence() {
        return sequence;
    }

    public void setSequence(int sequence) {
        this.sequence = sequence;
    }

    public String getCalendarUid() {
        return calendarUid;
    }

    public void setCalendarUid(String calendarUid) {
        this.calendarUid = calendarUid;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public String getFromName() {
        return fromName;
    }

    public void setFromName(String fromName) {
        this.fromName = fromName;
    }

    public List<String> getTo() {
        return to;
    }

    public void setTo(List<String> to) {
        this.to = to;
    }

    public List<String> getCc() {
        return cc;
    }

    public void setCc(List<String> cc) {
        this.cc = cc;
    }

    public List<String> getBcc() {
        return bcc;
    }

    public void setBcc(List<String> bcc) {
        this.bcc = bcc;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<EmailAttModel> getAttList() {
        return attList;
    }

    public void setAttList(List<EmailAttModel> attList) {
        this.attList = attList;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getMessage_id() {
        return message_id;
    }

    public void setMessage_id(String message_id) {
        this.message_id = format_message_id(message_id);
    }

    public String getReply_message_id() {
        return reply_message_id;
    }

    public void setReply_message_id(String reply_message_id) {
        this.reply_message_id = format_message_id(reply_message_id);
    }

    /**
     * 邮件的Message Id根据RFC5322要求必须是<开头,>结尾
     * @param message_id
     * @return
     */
    private String format_message_id(String message_id) {
        if (StringUtils.isEmpty(message_id)) {
            return message_id;
        }

        return "<" + message_id.replace("<","").replace(">","") + ">";
    }

    public Map<String, Object> getExtra_params() {
        return extra_params;
    }

    public void setExtra_params(Map<String, Object> extra_params) {
        this.extra_params = extra_params;
    }

    public List<String> getReply_to_list() {
        return reply_to_list;
    }

    public void setReply_to_list(List<String> reply_to_list) {
        this.reply_to_list = reply_to_list;
    }

    public String getRepeatRule() {
        return repeatRule;
    }

    public void setRepeatRule(String repeatRule) {
        this.repeatRule = repeatRule;
    }

    public Date getRepeatEndTime() {
        return repeatEndTime;
    }

    public void setRepeatEndTime(Date repeatEndTime) {
        this.repeatEndTime = repeatEndTime;
    }

    public int getInterval() {
        return interval;
    }

    public void setInterval(int interval) {
        this.interval = interval;
    }

    public int getDayOfMonth() {
        return dayOfMonth;
    }

    public void setDayOfMonth(int dayOfMonth) {
        this.dayOfMonth = dayOfMonth;
    }

    public List<DayOfWeek> getDaysOfWeek() {
        return daysOfWeek;
    }

    public void setDaysOfWeek(List<DayOfWeek> daysOfWeek) {
        this.daysOfWeek = daysOfWeek;
    }

    public DayOfWeek getFirstDayOfWeek() {
        return firstDayOfWeek;
    }

    public void setFirstDayOfWeek(DayOfWeek firstDayOfWeek) {
        this.firstDayOfWeek = firstDayOfWeek;
    }

    public int getMonth() {
        return month;
    }

    public void setMonth(int month) {
        this.month = month;
    }

    public WeekIndex getWeekIndex() {
        return weekIndex;
    }

    public void setWeekIndex(WeekIndex weekIndex) {
        this.weekIndex = weekIndex;
    }

    public List<String> getOptional_to() {
        return optional_to;
    }

    public void setOptional_to(List<String> optional_to) {
        this.optional_to = optional_to;
    }

    public Map<String, String> getHeads() {
        return heads;
    }

    public void setHeads(Map<String, String> heads) {
        this.heads = heads;
    }

    public String getPrefer_vendor() {
        return prefer_vendor;
    }

    public void setPrefer_vendor(String prefer_vendor) {
        this.prefer_vendor = prefer_vendor;
    }

    @Override
    public String toString() {
        return "EmailModel{" +
                "type='" + type + '\'' +
                ", from='" + from + '\'' +
                ", fromName='" + fromName + '\'' +
                ", to=" + to +
                ", cc=" + cc +
                ", bcc=" + bcc +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", location='" + location + '\'' +
                ", attList=" + attList +
                ", custom_params='" + extra_params + '\'' +
                ", subject='" + subject + '\'' +
                ", content='" + content + '\'' +
                '}';
    }

}
