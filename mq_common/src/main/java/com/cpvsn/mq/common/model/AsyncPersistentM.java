package com.cpvsn.mq.common.model;

/**
 * Created by cosx on 2016/9/6.
 */
public class AsyncPersistentM<T> {

    private int type;
    private Object data;

    public AsyncPersistentM() {
    }

    public AsyncPersistentM(int type, Object data) {
        this.type = type;
        this.data = data;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
