package com.cpvsn.mq.common.util;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;

/**
 * Created by pingli on 2017/10/20.
 */
public class ExceptionUtil {

    /**
     * 获取异常的详细信息
     */
    public static String getExceptionInfo(Exception e) {
        /** e.getLocalizedMessage()和e.getMessage()无法获取完整的异常信息，只是获取一个异常类的信息，printStrackTrace()中虽然有出错点信息，但都打到控制台上去了，Exception.getStackTrace()，并不能获得出错点的提示信息，
         * 使用e.printStackTrace(PrintStream)方法，将异常栈信息先输出到ByteOutputStream ，然后再将ByteOutputStream 转换为字符串，就获得了异常的完整输出
         */
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        e.printStackTrace(new PrintStream(outputStream));
        String exceptionInfo = outputStream.toString();

        return exceptionInfo;
    }
}
