api_host=api.capvision.cn
ndb_host=db.capvision.com

# 意向领取地址
intent_receive_link=http://db.capvision.com/project/consultation/ksexpt/assign
intent_receive_link_wap=http://www.capvision.com/h5assign/index.html?link_id=

# 客户身份确认
client_email_confirm_link=https://www.capvision.com/verifyClientRole?validate_token=

# push
app_key=bede4114fba4c0dcefd1334d
master_secret=e784420b223f6272fb06ea68

# xun fei voice conversion
app_id=5911391e
secret_key=8330f8bd75c2c4039c66752b36607bbd
voice_local_file=/tmp/voice_conversion_download

# e_mail
ntp_mail_group=<EMAIL>

# delay msg delta
delay_msg_delta=60000

#客户和客户联系人profile页地址
client_profile_url=http://db.capvision.com/pn/app/#/client/index/detail/view?client_id=
client_contact_profile_url=http://db.capvision.com/pn/app/#/client/contact/detail/view?contact_id=

# 获取word template url 8088端口
# word_gen_url=http://*************:8088/api/InvoiceTemplateInfo
# 获取word模板新端口
word_gen_url=http://*************:8055/api/invoice_template/official

# sms
sms_persist_url=http://db.api/cm-web/volunteer/sms


# 默认的代理发送邮件账号
proxy_send_email_default=<EMAIL>
# 用于代理发送邮件的邮件账号列表
proxy_send_email_list=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
# 用于代理发送邮件的邮件账号密码
proxy_send_email_password=DaPhDez4nxJCdn7
# 所有rabbitmq 队列的消费者handler的route key的后缀，即最终消费者的route key=handler注解的route key属性加上 后缀
mq_route_key_suffix=sg
# outreachHandler使用的发送方式：email 公司邮箱代理发送 sendgrid 群发
outreach_mail_send_type=email
# outreach 十分钟内最多可以发送的邮件量
outreach_mail_quota=500
# 公司邮件代理发送邮件的方式 exchange 和 microsoft_graph_api
mail_send_type=microsoft_graph_api
# 发送邮件默认使用的供应商
mail_send_default_vendor=EXCHANGE
