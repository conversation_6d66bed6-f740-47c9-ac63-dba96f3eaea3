package com.cpvsn.mq.test;

import com.alibaba.fastjson.JSONObject;
import com.cpvsn.core.factory.CoreFactory;
import com.cpvsn.core.service.RedisService;
import com.cpvsn.mq.common.exception.MqMailException;
import com.cpvsn.mq.common.model.email.BatchEmailModel;
import com.cpvsn.mq.common.model.email.EmailAttModel;
import com.cpvsn.mq.common.model.email.EmailModel;
import com.cpvsn.mq.constant.MailConst;
import com.cpvsn.mq.constant.QueueConst;
import com.cpvsn.mq.mail.GoogleApiSender;
import com.cpvsn.mq.service.MailService;
import com.cpvsn.mq.service.impl.*;
import com.cpvsn.mq.util.DateUtil;
import com.cpvsn.mq.util.MqUtil;
//import com.cpvsn.web.cm.core.util.StringUtil;
import com.mailjet.client.resource.Email;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.MailSendException;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

//import com.cpvsn.web.cm.core.util.StringUtil;

/**
 * Created by cosx on 2015/10/18.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring.xml")
public class ServiceTest {

    @Autowired
    MailService mailService;

    @Autowired
    SendgridMailService sendgridMailService;
    @Autowired
    PostMarkService postMarkService;
    @Autowired
    private ResendService resendService;
    @Autowired
    private MailgunService mailgunService;
    @Autowired
    private MailjetService mailjetService;

    private RedisService redisService = CoreFactory.getRedis();



    @Test
    public void testSendAgreementEmail() {
//        System.out.println(mailService.sendAgreementEmail("<EMAIL>" ,"en"));
    }


    @Test
    public void testSendgrid() throws Exception {
        String str = "{\"cc\":[],\"sequence\":0,\"bcc\":[],\"subject\":\" Capvision Survey - test survey project\",\"attList\":[],\"to\":[\"<EMAIL>\"],\"content\":\"<p style=\\\"font-family: Calibri;\\\">\\n\\n\\n    <meta charset=\\\"UTF-8\\\">\\n    <title>Title</title>\\n\\n\\n\\n<span style=\\\"font-family: Calibri;\\\">\uFEFF</span><span style=\\\"font-family: Calibri;\\\">\uFEFFvvvvbbbb</span>Hi muneeswari,\\n<br>\\n<br>\\n\\nMy name is Northern Chen and I work at Capvision, a primary research firm in New York. I'm currently working with a client,\\na <span></span> firm, who's interested in learning more about <span>测试 创建项目时无客户联系人</span>.\\nSpecifically, they want to understand __OUTREACH_DESCRIPTION__.\\nGiven your experience, I thought you'd be a good resource for this request.\\n<br>\\n<br>\\n\\nIf you’re interested, we’d set up a call between you and our client.\\nThe call will be roughly 1 hour and for your time on the phone we’d compensate you at a rate you determine.\\n<br>\\n<br>\\nFeel free to respond here or reach out by phone at 021-5229-9119.\\n\\n<br>\\n<br>\\nBest,\\n<br>\\nNorthern Chen | Front Engineer\\n<br>\\n135 E 57th St, 8th Floor | New York, NY 10022\\n<br>\\np: 021-5229-9119 e: <EMAIL>\\n<br>\\n<img alt=\\\"Capvision Logo\\\" src=\\\"cid:logo.png\\\">,src=\\\"cid:logo.png\\\"\\n\\n\\n</p>\"}";
        EmailModel model = JSONObject.parseObject(str, EmailModel.class);
        System.out.println(model);
        model.setContent("<div style=\\\"font-size: 11pt; font-family: Calibri;\\\">Hi 文轩__name__,<br /><br />I'm working with a client who is hoping to learn more about test display project. Given your experience, I thought you'd be a good person to reach out to for a brief conversation.<br /><br />Our end goal would be to schedule a ~45-60 minute phone consultation between an expert such as yourself and my client, for which we are of course happy to compensate.<br /><br />Do you have a 3-5 minutes today to briefly discuss the request? Please let me know the best time and number to reach you or feel free to call me at your convenience at 86-18221195569.<br /><br />Best,<br /><div class=\\\"WordSection1\\\">\\n<p class=\\\"MsoNormal\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 18.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision</span></strong><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 16.0pt; color: #1f497d; mso-no-proof: yes;\\\"> Partners</span></strong></span></p>\\n</div>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">Shanghai |Hong Kong |Beijing | New York |Shenzhen</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; font-family: 'Tahoma','sans-serif'; color: #999999; mso-no-proof: yes;\\\"> </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">|Berlin</span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Ping Li </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">李平</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> | </span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision Partners Co., Ltd.</span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Room 801-803, Greentech Tower, No.436, Hengfeng Road, Zhabei District, Shanghai 200070, PRC</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">中国上海市闸北区恒丰路</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">436</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">号环智国际大厦</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> 801-803</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">室（邮编</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">  200070</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">）</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Tel: (86) 21-6056-1968 Ext: | Mobile: (86) 18221195569 | Fax</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">：</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">(86)21- 3251-1247</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span lang=\\\"EN-US\\\"> </span></p> | Research Manager<br />135 E 57th St, 8th Floor | New York, NY 10022<br />p: 86-18221195569 e: <EMAIL></div>");
        model.setFrom("<EMAIL>");
        model.setFromName("Ping Li");
        model.setTo(Arrays.asList("<EMAIL>"));
        model.setCc(Arrays.asList("<EMAIL>"));
        model.setBcc(Arrays.asList("<EMAIL>"));
        String subject = model.getSubject();
        String content = model.getContent();
        for (int i = 0; i < 1600 ; i++) {
//            model.setSubject(subject + "---" + StringUtil.short_uuid());
//            model.setContent(content.replace("__name__", StringUtil.short_uuid()));
//            sendgridMailService.sendMail(model);

            JSONObject mailObj =  new JSONObject();
            mailObj.put("msg_type", "Mail");
            mailObj.put("data", model);


            MqUtil.sendMailMsg(mailObj.toJSONString(), QueueConst.QUEUE_MAIL_OUTREACH_RATE);
        }

    }

    @Test
    public void testSendGridWithCustomArgs() throws Exception {
        String str = "{\"cc\":[],\"sequence\":0,\"bcc\":[],\"subject\":\" Capvision Survey - test survey project\",\"attList\":[],\"to\":[\"<EMAIL>\"],\"content\":\"<p style=\\\"font-family: Calibri;\\\">\\n\\n\\n    <meta charset=\\\"UTF-8\\\">\\n    <title>Title</title>\\n\\n\\n\\n<span style=\\\"font-family: Calibri;\\\">\uFEFF</span><span style=\\\"font-family: Calibri;\\\">\uFEFFvvvvbbbb</span>Hi muneeswari,\\n<br>\\n<br>\\n\\nMy name is Northern Chen and I work at Capvision, a primary research firm in New York. I'm currently working with a client,\\na <span></span> firm, who's interested in learning more about <span>测试 创建项目时无客户联系人</span>.\\nSpecifically, they want to understand __OUTREACH_DESCRIPTION__.\\nGiven your experience, I thought you'd be a good resource for this request.\\n<br>\\n<br>\\n\\nIf you’re interested, we’d set up a call between you and our client.\\nThe call will be roughly 1 hour and for your time on the phone we’d compensate you at a rate you determine.\\n<br>\\n<br>\\nFeel free to respond here or reach out by phone at 021-5229-9119.\\n\\n<br>\\n<br>\\nBest,\\n<br>\\nNorthern Chen | Front Engineer\\n<br>\\n135 E 57th St, 8th Floor | New York, NY 10022\\n<br>\\np: 021-5229-9119 e: <EMAIL>\\n<br>\\n<img alt=\\\"Capvision Logo\\\" src=\\\"cid:logo.png\\\">,src=\\\"cid:logo.png\\\"\\n\\n\\n</p>\"}";
        EmailModel model = JSONObject.parseObject(str, EmailModel.class);
        System.out.println(model);
        model.setContent("<div style=\\\"font-size: 11pt; font-family: Calibri;\\\">Hi 文轩2,<br /><br />I'm working with a client who is hoping to learn more about test display project. Given your experience, I thought you'd be a good person to reach out to for a brief conversation.<br /><br />Our end goal would be to schedule a ~45-60 minute phone consultation between an expert such as yourself and my client, for which we are of course happy to compensate.<br /><br />Do you have a 3-5 minutes today to briefly discuss the request? Please let me know the best time and number to reach you or feel free to call me at your convenience at 86-18221195569.<br /><br />Best,<br /><div class=\\\"WordSection1\\\">\\n<p class=\\\"MsoNormal\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 18.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision</span></strong><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 16.0pt; color: #1f497d; mso-no-proof: yes;\\\"> Partners</span></strong></span></p>\\n</div>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">Shanghai |Hong Kong |Beijing | New York |Shenzhen</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; font-family: 'Tahoma','sans-serif'; color: #999999; mso-no-proof: yes;\\\"> </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">|Berlin</span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Ping Li </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">李平</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> | </span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision Partners Co., Ltd.</span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Room 801-803, Greentech Tower, No.436, Hengfeng Road, Zhabei District, Shanghai 200070, PRC</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">中国上海市闸北区恒丰路</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">436</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">号环智国际大厦</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> 801-803</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">室（邮编</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">  200070</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">）</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Tel: (86) 21-6056-1968 Ext: | Mobile: (86) 18221195569 | Fax</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">：</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">(86)21- 3251-1247</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span lang=\\\"EN-US\\\"> </span></p> | Research Manager<br />135 E 57th St, 8th Floor | New York, NY 10022<br />p: 86-18221195569 e: <EMAIL></div>");
        model.setFrom("<EMAIL>");
        model.setFromName("whatever");
        model.setTo(Arrays.asList("<EMAIL>", "<EMAIL>"));
        Map<String, Object> map = new HashMap<>();
        map.put(SendgridMailService.SENDGRID_CUSTOM_ARG_PREFIX + "email_record_id", "1");
        model.setExtra_params(map);
        model.setReply_to_list(Collections.singletonList("<EMAIL>"));
        model.setMessage_id("<dsfjik3owerjewkrj12321_CAPVISION-rm-web$<EMAIL>>");
        mailService.filterEmailModel(model);

        sendgridMailService.sendMail(model);
    }

    @Test
    public void testSend() throws Exception{
        EmailModel emailModel = new EmailModel();
        String test = "org.springframework.mail.MailSendException: Mail server connection failed; nested exception is javax.mail.MessagingException: Exception reading response; \n" +
                "  nested exception is: \n" +
                "    java.net.SocketTimeoutException: Read timed out. Failed messages: javax.mail.MessagingException: Exception reading response; \n" +
                "  nested exception is: \n" +
                "    java.net.SocketTimeoutException: Read timed out; message exception details (1) are: \n" +
                "Failed message 1: \n" +
                "javax.mail.MessagingException: Exception reading response; \n" +
                "  nested exception is: \n" +
                "    java.net.SocketTimeoutException: Read timed out \n" +
                "    at com.sun.mail.smtp.SMTPTransport.readServerResponse(SMTPTransport.java:2202) \n" +
                "    at com.sun.mail.smtp.SMTPTransport.openServer(SMTPTransport.java:1939) \n" +
                "    at com.sun.mail.smtp.SMTPTransport.protocolConnect(SMTPTransport.java:654) \n" +
                "    at javax.mail.Service.connect(Service.java:295) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.connectTransport(JavaMailSenderImpl.java:486) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.doSend(JavaMailSenderImpl.java:406) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.send(JavaMailSenderImpl.java:345) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.send(JavaMailSenderImpl.java:340) \n" +
                "    at com.cpvsn.mq.mail.ExchangeSender.sendMail(ExchangeSender.java:341) \n" +
                "    at com.cpvsn.mq.mail.ExchangeSender.sendWithAtt(ExchangeSender.java:145) \n" +
                "    at com.cpvsn.mq.service.impl.MailServiceImpl.sendMail(MailServiceImpl.java:101) \n" +
                "    at com.cpvsn.mq.handler.MailHandler.handleMessage(MailHandler.java:59) \n" +
                "    at com.cpvsn.mq.common.listener.AcknowledgeMqListener.onMessage(AcknowledgeMqListener.java:28) \n" +
                "    at org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter.onMessage(MessageListenerAdapter.java:228) \n" +
                "    at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.doInvokeListener(AbstractMessageListenerContainer.java:733) \n" +
                "    at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.invokeListener(AbstractMessageListenerContainer.java:656) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.access$001(SimpleMessageListenerContainer.java:82) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$1.invokeListener(SimpleMessageListenerContainer.java:167) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.invokeListener(SimpleMessageListenerContainer.java:1196) \n" +
                "    at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.executeListener(AbstractMessageListenerContainer.java:637) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.doReceiveAndExecute(SimpleMessageListenerContainer.java:960) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.receiveAndExecute(SimpleMessageListenerContainer.java:944) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.access$700(SimpleMessageListenerContainer.java:82) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1058) \n" +
                "    at java.lang.Thread.run(Thread.java:745) \n" +
                "Caused by: java.net.SocketTimeoutException: Read timed out \n" +
                "    at java.net.SocketInputStream.socketRead0(Native Method) \n" +
                "    at java.net.SocketInputStream.read(SocketInputStream.java:152) \n" +
                "    at java.net.SocketInputStream.read(SocketInputStream.java:122) \n" +
                "    at com.sun.mail.util.TraceInputStream.read(TraceInputStream.java:124) \n" +
                "    at java.io.BufferedInputStream.fill(BufferedInputStream.java:235) \n" +
                "    at java.io.BufferedInputStream.read(BufferedInputStream.java:254) \n" +
                "    at com.sun.mail.util.LineInputStream.readLine(LineInputStream.java:89) \n" +
                "    at com.sun.mail.smtp.SMTPTransport.readServerResponse(SMTPTransport.java:2182) \n" +
                "    ... 24 more \n";
        if (test.indexOf("Caused by: java.net.SocketTimeoutException: Read timed out") != -1){
            System.out.println("*(***************************************************");
            System.out.println("ik");
        }
        try {
            emailModel.setType(MailConst.TYPE_EXCHANGE);
            emailModel.setFrom(null);
            emailModel.setFromName("coswid");
            List<String> toList = new ArrayList<>();
            toList.add("null");
            List<String> ccList = new ArrayList<>();
            ccList.add(null);
            emailModel.setTo(toList);
//            emailModel.setBcc(ccList);
            emailModel.setSubject("Hi");
            emailModel.setContent("这是封发自Sedncloud的测试邮件");
            String[] fromArray = new String[]{"sdf"};
            for (String s : fromArray) {
                System.out.println(s);
            }

            mailService.sendMail(emailModel);
        }catch (Exception e) {
            System.out.println("test sk");
            e.printStackTrace();

            /** e.getLocalizedMessage()和e.getMessage()无法获取完整的异常信息，只是获取一个异常类的信息，printStrackTrace()中虽然有出错点信息，但都打到控制台上去了，Exception.getStackTrace()，并不能获得出错点的提示信息，
             * 使用e.printStackTrace(PrintStream)方法，将异常栈信息先输出到ByteOutputStream ，然后再将ByteOutputStream 转换为字符串，就获得了异常的完整输出
             */
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            e.printStackTrace(new PrintStream(outputStream));
            String exceptionInfo = outputStream.toString();
            System.out.println(exceptionInfo);
            if (exceptionInfo.indexOf("Caused by: com.sun.mail.smtp.SMTPAddressFailedException") != -1) {
                System.out.println("***********************************************************************************************************");
            }
        }

    }

    @Test
    public void testRegexMatch() throws Exception {
        String str1 = "org.springframework.mail.MailSendException: Mail server connection failed; nested exception is javax.mail.MessagingException: Exception reading response; \n" +
                "  nested exception is: \n" +
                "    java.net.SocketTimeoutException: Read timed out. Failed messages: javax.mail.MessagingException: Exception reading response; \n" +
                "  nested exception is: \n" +
                "    java.net.SocketTimeoutException: Read timed out; message exception details (1) are: \n" +
                "Failed message 1: \n" +
                "javax.mail.MessagingException: Exception reading response; \n" +
                "  nested exception is: \n" +
                "    java.net.SocketTimeoutException: Read timed out \n" +
                "    at com.sun.mail.smtp.SMTPTransport.readServerResponse(SMTPTransport.java:2202) \n" +
                "    at com.sun.mail.smtp.SMTPTransport.openServer(SMTPTransport.java:1939) \n" +
                "    at com.sun.mail.smtp.SMTPTransport.protocolConnect(SMTPTransport.java:654) \n" +
                "    at javax.mail.Service.connect(Service.java:295) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.connectTransport(JavaMailSenderImpl.java:486) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.doSend(JavaMailSenderImpl.java:406) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.send(JavaMailSenderImpl.java:345) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.send(JavaMailSenderImpl.java:340) \n" +
                "    at com.cpvsn.mq.mail.ExchangeSender.sendWithAtt(ExchangeSender.java:149) \n" +
                "    at com.cpvsn.mq.service.impl.MailServiceImpl.sendMail(MailServiceImpl.java:100) \n" +
                "    at com.cpvsn.mq.handler.MailHandler.handleMessage(MailHandler.java:59) \n" +
                "    at com.cpvsn.mq.common.listener.AcknowledgeMqListener.onMessage(AcknowledgeMqListener.java:27) \n" +
                "    at org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter.onMessage(MessageListenerAdapter.java:228) \n" +
                "    at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.doInvokeListener(AbstractMessageListenerContainer.java:733) \n" +
                "    at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.invokeListener(AbstractMessageListenerContainer.java:656) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.access$001(SimpleMessageListenerContainer.java:82) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$1.invokeListener(SimpleMessageListenerContainer.java:167) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.invokeListener(SimpleMessageListenerContainer.java:1196) \n" +
                "    at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.executeListener(AbstractMessageListenerContainer.java:637) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.doReceiveAndExecute(SimpleMessageListenerContainer.java:960) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.receiveAndExecute(SimpleMessageListenerContainer.java:944) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.access$700(SimpleMessageListenerContainer.java:82) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1058) \n" +
                "    at java.lang.Thread.run(Thread.java:745) \n" +
                "Caused by: java.net.SocketTimeoutException: Read timed out \n" +
                "    at java.net.SocketInputStream.socketRead0(Native Method) \n" +
                "    at java.net.SocketInputStream.read(SocketInputStream.java:152) \n" +
                "    at java.net.SocketInputStream.read(SocketInputStream.java:122) \n" +
                "    at com.sun.mail.util.TraceInputStream.read(TraceInputStream.java:124) \n" +
                "    at java.io.BufferedInputStream.fill(BufferedInputStream.java:235) \n" +
                "    at java.io.BufferedInputStream.read(BufferedInputStream.java:254) \n" +
                "    at com.sun.mail.util.LineInputStream.readLine(LineInputStream.java:89) \n" +
                "    at com.sun.mail.smtp.SMTPTransport.readServerResponse(SMTPTransport.java:2182) \n" +
                "    ... 23 more \n";
        String str2 = "org.springframework.mail.MailSendException: Mail server connection failed; nested exception is javax.mail.MessagingException: [EOF]. Failed messages: javax.mail.MessagingException: [EOF]; message exception details (1) are: \n" +
                "Failed message 1: \n" +
                "javax.mail.MessagingException: [EOF] \n" +
                "    at com.sun.mail.smtp.SMTPTransport.issueCommand(SMTPTransport.java:2074) \n" +
                "    at com.sun.mail.smtp.SMTPTransport.helo(SMTPTransport.java:1469) \n" +
                "    at com.sun.mail.smtp.SMTPTransport.protocolConnect(SMTPTransport.java:660) \n" +
                "    at javax.mail.Service.connect(Service.java:295) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.connectTransport(JavaMailSenderImpl.java:486) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.doSend(JavaMailSenderImpl.java:406) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.send(JavaMailSenderImpl.java:345) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.send(JavaMailSenderImpl.java:340) \n" +
                "    at com.cpvsn.mq.mail.ExchangeSender.sendWithAtt(ExchangeSender.java:149) \n" +
                "    at com.cpvsn.mq.service.impl.MailServiceImpl.sendMail(MailServiceImpl.java:100) \n" +
                "    at com.cpvsn.mq.handler.MailHandler.handleMessage(MailHandler.java:59) \n" +
                "    at com.cpvsn.mq.common.listener.AcknowledgeMqListener.onMessage(AcknowledgeMqListener.java:27) \n" +
                "    at org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter.onMessage(MessageListenerAdapter.java:228) \n" +
                "    at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.doInvokeListener(AbstractMessageListenerContainer.java:733) \n" +
                "    at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.invokeListener(AbstractMessageListenerContainer.java:656) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.access$001(SimpleMessageListenerContainer.java:82) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$1.invokeListener(SimpleMessageListenerContainer.java:167) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.invokeListener(SimpleMessageListenerContainer.java:1196) \n" +
                "    at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.executeListener(AbstractMessageListenerContainer.java:637) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.doReceiveAndExecute(SimpleMessageListenerContainer.java:960) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.receiveAndExecute(SimpleMessageListenerContainer.java:944) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.access$700(SimpleMessageListenerContainer.java:82) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1058) \n" +
                "    at java.lang.Thread.run(Thread.java:745) \n";
        String str3 = "org.springframework.mail.MailSendException: Failed messages: javax.mail.MessagingException: Can't send command to SMTP host; \n" +
                "  nested exception is: \n" +
                "    java.net.SocketException: Connection closed by remote host; message exception details (1) are: \n" +
                "Failed message 1: \n" +
                "javax.mail.MessagingException: Can't send command to SMTP host; \n" +
                "  nested exception is: \n" +
                "    java.net.SocketException: Connection closed by remote host \n" +
                "    at com.sun.mail.smtp.SMTPTransport.sendCommand(SMTPTransport.java:2157) \n" +
                "    at com.sun.mail.smtp.SMTPTransport.sendCommand(SMTPTransport.java:2144) \n" +
                "    at com.sun.mail.smtp.SMTPTransport.issueCommand(SMTPTransport.java:2068) \n" +
                "    at com.sun.mail.smtp.SMTPTransport.issueSendCommand(SMTPTransport.java:2105) \n" +
                "    at com.sun.mail.smtp.SMTPTransport.data(SMTPTransport.java:1876) \n" +
                "    at com.sun.mail.smtp.SMTPTransport.sendMessage(SMTPTransport.java:1119) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.doSend(JavaMailSenderImpl.java:433) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.send(JavaMailSenderImpl.java:345) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.send(JavaMailSenderImpl.java:340) \n" +
                "    at com.cpvsn.mq.mail.ExchangeSender.sendMail(ExchangeSender.java:339) \n" +
                "    at com.cpvsn.mq.mail.ExchangeSender.sendWithAtt(ExchangeSender.java:143) \n" +
                "    at com.cpvsn.mq.service.impl.MailServiceImpl.sendMail(MailServiceImpl.java:101) \n" +
                "    at com.cpvsn.mq.handler.MailHandler.handleMessage(MailHandler.java:59) \n" +
                "    at com.cpvsn.mq.common.listener.AcknowledgeMqListener.onMessage(AcknowledgeMqListener.java:28) \n" +
                "    at org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter.onMessage(MessageListenerAdapter.java:228) \n" +
                "    at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.doInvokeListener(AbstractMessageListenerContainer.java:733) \n" +
                "    at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.invokeListener(AbstractMessageListenerContainer.java:656) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.access$001(SimpleMessageListenerContainer.java:82) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$1.invokeListener(SimpleMessageListenerContainer.java:167) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.invokeListener(SimpleMessageListenerContainer.java:1196) \n" +
                "    at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.executeListener(AbstractMessageListenerContainer.java:637) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.doReceiveAndExecute(SimpleMessageListenerContainer.java:960) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.receiveAndExecute(SimpleMessageListenerContainer.java:944) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.access$700(SimpleMessageListenerContainer.java:82) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1058) \n" +
                "    at java.lang.Thread.run(Thread.java:745) \n" +
                "Caused by: java.net.SocketException: Connection closed by remote host \n" +
                "    at sun.security.ssl.SSLSocketImpl.checkWrite(SSLSocketImpl.java:1510) \n" +
                "    at sun.security.ssl.AppOutputStream.write(AppOutputStream.java:70) \n" +
                "    at com.sun.mail.util.TraceOutputStream.write(TraceOutputStream.java:128) \n" +
                "    at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82) \n" +
                "    at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140) \n" +
                "    at com.sun.mail.smtp.SMTPTransport.sendCommand(SMTPTransport.java:2155) \n" +
                "    ... 25 more \n";
        String str4 = "org.springframework.mail.MailSendException: Mail server connection failed; nested exception is javax.mail.MessagingException: Could not convert socket to TLS; \n" +
                "  nested exception is: \n" +
                "    javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake. Failed messages: javax.mail.MessagingException: Could not convert socket to TLS; \n" +
                "  nested exception is: \n" +
                "    javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake; message exception details (1) are: \n" +
                "Failed message 1: \n" +
                "javax.mail.MessagingException: Could not convert socket to TLS; \n" +
                "  nested exception is: \n" +
                "    javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake \n" +
                "    at com.sun.mail.smtp.SMTPTransport.startTLS(SMTPTransport.java:1907) \n" +
                "    at com.sun.mail.smtp.SMTPTransport.protocolConnect(SMTPTransport.java:666) \n" +
                "    at javax.mail.Service.connect(Service.java:295) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.connectTransport(JavaMailSenderImpl.java:486) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.doSend(JavaMailSenderImpl.java:406) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.send(JavaMailSenderImpl.java:345) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.send(JavaMailSenderImpl.java:340) \n" +
                "    at com.cpvsn.mq.mail.ExchangeSender.sendMail(ExchangeSender.java:341) \n" +
                "    at com.cpvsn.mq.mail.ExchangeSender.sendWithAtt(ExchangeSender.java:145) \n" +
                "    at com.cpvsn.mq.service.impl.MailServiceImpl.sendMail(MailServiceImpl.java:101) \n" +
                "    at com.cpvsn.mq.handler.MailHandler.handleMessage(MailHandler.java:59) \n" +
                "    at com.cpvsn.mq.common.listener.AcknowledgeMqListener.onMessage(AcknowledgeMqListener.java:28) \n" +
                "    at org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter.onMessage(MessageListenerAdapter.java:228) \n" +
                "    at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.doInvokeListener(AbstractMessageListenerContainer.java:733) \n" +
                "    at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.invokeListener(AbstractMessageListenerContainer.java:656) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.access$001(SimpleMessageListenerContainer.java:82) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$1.invokeListener(SimpleMessageListenerContainer.java:167) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.invokeListener(SimpleMessageListenerContainer.java:1196) \n" +
                "    at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.executeListener(AbstractMessageListenerContainer.java:637) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.doReceiveAndExecute(SimpleMessageListenerContainer.java:960) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.receiveAndExecute(SimpleMessageListenerContainer.java:944) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.access$700(SimpleMessageListenerContainer.java:82) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1058) \n" +
                "    at java.lang.Thread.run(Thread.java:745) \n" +
                "Caused by: javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake \n" +
                "    at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:953) \n" +
                "    at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1332) \n" +
                "    at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1359) \n" +
                "    at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1343) \n" +
                "    at com.sun.mail.util.SocketFetcher.configureSSLSocket(SocketFetcher.java:549) \n" +
                "    at com.sun.mail.util.SocketFetcher.startTLS(SocketFetcher.java:486) \n" +
                "    at com.sun.mail.smtp.SMTPTransport.startTLS(SMTPTransport.java:1902) \n" +
                "    ... 23 more \n" +
                "Caused by: java.io.EOFException: SSL peer shut down incorrectly \n" +
                "    at sun.security.ssl.InputRecord.read(InputRecord.java:482) \n" +
                "    at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:934) \n" +
                "    ... 29 more \n";
        String str5 = "org.springframework.mail.MailSendException: Mail server connection failed; nested exception is javax.mail.MessagingException: Could not connect to SMTP host: smtp.partner.outlook.cn, port: 587, response: -1. Failed messages: javax.mail.MessagingException: Could not connect to SMTP host: smtp.partner.outlook.cn, port: 587, response: -1; message exception details (1) are: \n" +
                "Failed message 1: \n" +
                "javax.mail.MessagingException: Could not connect to SMTP host: smtp.partner.outlook.cn, port: 587, response: -1 \n" +
                "    at com.sun.mail.smtp.SMTPTransport.openServer(SMTPTransport.java:1949) \n" +
                "    at com.sun.mail.smtp.SMTPTransport.protocolConnect(SMTPTransport.java:654) \n" +
                "    at javax.mail.Service.connect(Service.java:295) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.connectTransport(JavaMailSenderImpl.java:486) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.doSend(JavaMailSenderImpl.java:406) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.send(JavaMailSenderImpl.java:345) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.send(JavaMailSenderImpl.java:340) \n" +
                "    at com.cpvsn.mq.mail.ExchangeSender.sendWithAtt(ExchangeSender.java:149) \n" +
                "    at com.cpvsn.mq.service.impl.MailServiceImpl.sendMail(MailServiceImpl.java:100) \n" +
                "    at com.cpvsn.mq.handler.MailHandler.handleMessage(MailHandler.java:59) \n" +
                "    at com.cpvsn.mq.common.listener.AcknowledgeMqListener.onMessage(AcknowledgeMqListener.java:27) \n" +
                "    at org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter.onMessage(MessageListenerAdapter.java:228) \n" +
                "    at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.doInvokeListener(AbstractMessageListenerContainer.java:733) \n" +
                "    at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.invokeListener(AbstractMessageListenerContainer.java:656) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.access$001(SimpleMessageListenerContainer.java:82) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$1.invokeListener(SimpleMessageListenerContainer.java:167) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.invokeListener(SimpleMessageListenerContainer.java:1196) \n" +
                "    at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.executeListener(AbstractMessageListenerContainer.java:637) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.doReceiveAndExecute(SimpleMessageListenerContainer.java:960) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.receiveAndExecute(SimpleMessageListenerContainer.java:944) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.access$700(SimpleMessageListenerContainer.java:82) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1058) \n" +
                "    at java.lang.Thread.run(Thread.java:745) \n";
        String str6 = "org.springframework.mail.MailSendException: Failed messages: com.sun.mail.smtp.SMTPSendFailedException: 250 2.6.0 <1033830376.94047.1505267581088.JavaMail.javamailuser@localhost> [InternalId=129209796135002, Hostname=SH2PR01MB268.CHNPR01.prod.partner.outlook.cn] 5458 bytes in 0.238, 22.336 KB/sec Queued mail for delivery \n" +
                "; \n" +
                "  nested exception is: \n" +
                "    com.sun.mail.smtp.SMTPAddressFailedException: 501 5.1.3 Invalid address [SH2PR01MB268.CHNPR01.prod.partner.outlook.cn] \n" +
                "; message exception details (1) are: \n" +
                "Failed message 1: \n" +
                "com.sun.mail.smtp.SMTPSendFailedException: 250 2.6.0 <1033830376.94047.1505267581088.JavaMail.javamailuser@localhost> [InternalId=129209796135002, Hostname=SH2PR01MB268.CHNPR01.prod.partner.outlook.cn] 5458 bytes in 0.238, 22.336 KB/sec Queued mail for delivery \n" +
                "; \n" +
                "  nested exception is: \n" +
                "    com.sun.mail.smtp.SMTPAddressFailedException: 501 5.1.3 Invalid address [SH2PR01MB268.CHNPR01.prod.partner.outlook.cn] \n" +
                "\n" +
                "    at com.sun.mail.smtp.SMTPTransport.sendMessage(SMTPTransport.java:1131) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.doSend(JavaMailSenderImpl.java:433) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.send(JavaMailSenderImpl.java:345) \n" +
                "    at org.springframework.mail.javamail.JavaMailSenderImpl.send(JavaMailSenderImpl.java:340) \n" +
                "    at com.cpvsn.mq.mail.ExchangeSender.sendWithAtt(ExchangeSender.java:145) \n" +
                "    at com.cpvsn.mq.service.impl.MailServiceImpl.sendMail(MailServiceImpl.java:100) \n" +
                "    at com.cpvsn.mq.handler.MailHandler.handleMessage(MailHandler.java:60) \n" +
                "    at sun.reflect.GeneratedMethodAccessor21.invoke(Unknown Source) \n" +
                "    at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) \n" +
                "    at java.lang.reflect.Method.invoke(Method.java:606) \n" +
                "    at org.springframework.util.MethodInvoker.invoke(MethodInvoker.java:269) \n" +
                "    at org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter.invokeListenerMethod(MessageListenerAdapter.java:327) \n" +
                "    at org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter.onMessage(MessageListenerAdapter.java:253) \n" +
                "    at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.doInvokeListener(AbstractMessageListenerContainer.java:733) \n" +
                "    at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.invokeListener(AbstractMessageListenerContainer.java:656) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.access$001(SimpleMessageListenerContainer.java:82) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$1.invokeListener(SimpleMessageListenerContainer.java:167) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.invokeListener(SimpleMessageListenerContainer.java:1196) \n" +
                "    at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.executeListener(AbstractMessageListenerContainer.java:637) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.doReceiveAndExecute(SimpleMessageListenerContainer.java:960) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.receiveAndExecute(SimpleMessageListenerContainer.java:944) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.access$700(SimpleMessageListenerContainer.java:82) \n" +
                "    at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1058) \n" +
                "    at java.lang.Thread.run(Thread.java:745) \n" +
                "Caused by: com.sun.mail.smtp.SMTPAddressFailedException: 501 5.1.3 Invalid address [SH2PR01MB268.CHNPR01.prod.partner.outlook.cn] \n" +
                "\n" +
                "    at com.sun.mail.smtp.SMTPTransport.rcptTo(SMTPTransport.java:1715) \n" +
                "    at com.sun.mail.smtp.SMTPTransport.sendMessage(SMTPTransport.java:1118) \n" +
                "    ... 23 more \n";
        String regex = "(Could not connect to SMTP host)|(Could not convert socket to TLS)|(java\\.net\\.SocketTimeoutException: Read timed out)|(javax.mail.MessagingException: \\[EOF\\])|(Can't send command to SMTP host)";
        Pattern pattern = Pattern.compile(regex, Pattern.MULTILINE);
        Matcher matcher = pattern.matcher(str1);
        System.out.println();
        if (matcher.find()){
            System.out.println("1ok");
        }
        matcher = pattern.matcher(str2);
        if (matcher.find()){
            System.out.println("2ok");
        }
        matcher = pattern.matcher(str3);
        if (matcher.find()){
            System.out.println("3ok");
        }
        matcher = pattern.matcher(str4);
        if (matcher.find()){
            System.out.println("4ok");
        }
        matcher = pattern.matcher(str5);
        if (matcher.find()){
            System.out.println("5ok");
        }
        matcher = pattern.matcher(str6);
        if (matcher.find()){
            System.out.println("6ok");
        }

    }

    @Test
    public void testCheckAddressList() throws Exception {
        String [] test = new String[] {"232         3   er  "};
//        String [] he = ExchangeSender.checkMailAddress(test);
        System.out.println("id");
        try{
            throw new MqMailException("dfs");
        }catch (MqMailException e) {
            System.out.println("id");
        }catch (Exception e) {
            System.out.println("is");
            return;
        }
        System.out.println("test");
    }

    @Test
    public void testSendMailPortal() throws Exception {
        EmailModel model = new EmailModel();
        model.setFrom("<EMAIL>");
        model.setFromName("compliance portal");
        List<String> to = new ArrayList<>();
//        to.add("<EMAIL>");
        to.add("<EMAIL>");
        List<String> cc = new ArrayList<>();
        List<String> bcc = new ArrayList<>();
        bcc.add("<EMAIL>");
        bcc.add("<EMAIL>");
        bcc.add("<EMAIL>");
        model.setTo(to);
        model.setCc(cc);
        model.setBcc(bcc);
        model.setSubject("send cloud calendar");
        model.setContent("测试send cloud 群发日历");
        System.out.println(mailService.sendMail(model));
    }

    @Test
    public void testSendMail12() {
        String msg = "{\"to\":[\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\"],\"content\":\"<table><tbody><tr><td align=\\\"left\\\" style=\\\"width:60%\\\"></td>\\n<td align=\\\"right\\\"><img border=\\\"0\\\" id=\\\"图片_x0020_2\\\" src=\\\"http://cdn.capvision.cn/img/ksh/new_logo.png\\\"></td>\\n</tr></tbody></table>\\nHi,<br/>The following consultation was recently approved in the Capvision Compliance Portal.<br/> Please see the consultation details below for your records.<br/><br/><br/><strong>Consultation Details: </strong><br/><strong>Expert Name: </strong>fengxiang wang<br/><strong>Expert Company: </strong>China literature(Current)<br/><strong>Expert Position: </strong>Signed Writer(Current)<br/><br/><strong>Project: </strong>Online Reading_CON1806050372<br/><strong>Client User: </strong>Jessica Tjhin<br/><br/><strong>Approved comment: </strong>(None)<br/><strong>Approved by: </strong>Jessica Tjhin at 2018-06-05 16:38:15<br/><br/><p style=\\\"text-align:justify;text-justify:inter-ideograph\\\">Click <a href = \\\"http://compliance.capvision.com/#/platform/task?task_id=849390\\\">here</a> to review the consultation on our portal.</p>Many thanks,<br/><div>\\n<div style=\\\"word-wrap:break-word; color:rgb(0,0,0); font-size:14px; font-family:宋体,sans-serif\\\">\\n<div><br>\\n</div>\\n<p class=\\\"x_MsoNormal\\\" style=\\\"margin:0cm 0cm 0.0001pt; font-size:12pt; font-family:宋体; text-align:justify\\\">\\n<b><span lang=\\\"EN-US\\\" style=\\\"font-size:10.5pt; font-family:微软雅黑,sans-serif; color:rgb(31,73,125)\\\">Capvision Partners (Shanghai) Co., Ltd.</span></b></p>\\n<p class=\\\"x_MsoNormal\\\" style=\\\"margin:0cm 0cm 0.0001pt; font-size:12pt; font-family:宋体; text-align:justify\\\">\\n<b><span lang=\\\"EN-US\\\" style=\\\"font-size:10pt; font-family:Calibri,sans-serif; color:rgb(31,73,125)\\\">T</span></b><span lang=\\\"EN-US\\\" style=\\\"font-size:10pt; font-family:Calibri,sans-serif; color:rgb(31,73,125)\\\">: (86) 21-3158-0888</span></p>\\n<p class=\\\"x_MsoNormal\\\" style=\\\"margin:0cm 0cm 0.0001pt; font-size:12pt; font-family:宋体; text-align:justify\\\">\\n<b><span lang=\\\"EN-US\\\" style=\\\"font-size:10pt; font-family:Calibri,sans-serif; color:rgb(31,73,125)\\\">F</span></b><span lang=\\\"EN-US\\\" style=\\\"font-size:10pt; font-family:Calibri,sans-serif; color:rgb(31,73,125)\\\">: (86) 21-3251-1247</span></p>\\n<p class=\\\"x_MsoNormal\\\" style=\\\"margin:0cm 0cm 0.0001pt; font-size:12pt; font-family:宋体; text-align:justify\\\">\\n<b><span lang=\\\"EN-US\\\" style=\\\"font-size:10pt; font-family:Calibri,sans-serif; color:rgb(31,73,125)\\\">A</span></b><span lang=\\\"EN-US\\\" style=\\\"font-size:10pt; font-family:Calibri,sans-serif; color:rgb(31,73,125)\\\">: F/8, No.436, Hengfeng Road, Shanghai 200070, PRC</span></p>\\n<p class=\\\"x_MsoNormal\\\" style=\\\"margin:0cm 0cm 0.0001pt; font-size:12pt; font-family:宋体; text-align:justify\\\">\\n<span lang=\\\"EN-US\\\" style=\\\"font-size:11pt; font-family:Calibri,sans-serif; color:rgb(31,73,125)\\\"><img  src=\\\"http://static.capvision.cn/img/cms/2016-08/1471942617.82.jpg\\\" width=\\\"296\\\" height=\\\"107\\\" id=\\\"x__x0000_i1025\\\" alt=\\\"APP签名栏-EN\\\" type=\\\"image/png\\\" style=\\\"display: inline;\\\"></span></p>\\n<p class=\\\"x_MsoNormal\\\" style=\\\"margin:0cm 0cm 0.0001pt; font-size:12pt; font-family:宋体\\\">\\n<span lang=\\\"EN-US\\\" style=\\\"font-size:10.5pt; font-family:Calibri,sans-serif; color:rgb(31,73,125)\\\">&nbsp;</span></p>\\n</div>\\n\\n</div>\",\"subject\":\"Task approval for Online Reading\",\"bcc\":[],\"fromName\":\"Capvision noreply\",\"from\":\"<EMAIL>\",\"type\":\"002\",\"cc\":[]}\n";

        EmailModel model = JSONObject.parseObject(msg, EmailModel.class);
        System.out.println(model);

        try {
            mailService.sendMail(model);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Test
    public void testname() throws Exception {
        String str = "{\"cc\":[],\"sequence\":0,\"bcc\":[],\"subject\":\"aaExpertise Request – 测试 创建项目时无客户联系人\",\"attList\":[],\"to\":[\"<EMAIL>\"],\"content\":\"<p style=\\\"font-family: Calibri;\\\">\\n\\n\\n    <meta charset=\\\"UTF-8\\\">\\n    <title>Title</title>\\n\\n\\n\\n<span style=\\\"font-family: Calibri;\\\">\uFEFF</span><span style=\\\"font-family: Calibri;\\\">\uFEFFvvvvbbbb</span>Hi muneeswari,\\n<br>\\n<br>\\n\\nMy name is Northern Chen and I work at Capvision, a primary research firm in New York. I'm currently working with a client,\\na <span></span> firm, who's interested in learning more about <span>测试 创建项目时无客户联系人</span>.\\nSpecifically, they want to understand __OUTREACH_DESCRIPTION__.\\nGiven your experience, I thought you'd be a good resource for this request.\\n<br>\\n<br>\\n\\nIf you’re interested, we’d set up a call between you and our client.\\nThe call will be roughly 1 hour and for your time on the phone we’d compensate you at a rate you determine.\\n<br>\\n<br>\\nFeel free to respond here or reach out by phone at 021-5229-9119.\\n\\n<br>\\n<br>\\nBest,\\n<br>\\nNorthern Chen | Front Engineer\\n<br>\\n135 E 57th St, 8th Floor | New York, NY 10022\\n<br>\\np: 021-5229-9119 e: <EMAIL>\\n<br>\\n<img alt=\\\"Capvision Logo\\\" src=\\\"cid:logo.png\\\">,src=\\\"cid:logo.png\\\"\\n\\n\\n</p>\"}";
        EmailModel model = JSONObject.parseObject(str, EmailModel.class);
        System.out.println(model);
        model.setPrefer_vendor("EXCHANGE");
        model.setFrom(MailConst.SYSTEM_EMAIL);
        model.setFromName(MailConst.SYSTEM_EMAIL_NAME);
        model.getTo().add("<EMAIL>");
        model.setMessage_id("<fdsjksldf22342353465456_CAPVISION-rm-web$<EMAIL>>");
//        model.setReply_message_id("<fdsjksldf2342353465456_CAPVISION-rm-web$<EMAIL>>");

        mailService.sendMail(model);
    }

    @Test
    public void testSendMq() {
        EmailModel emailModel = new EmailModel();
        try {
            emailModel.setType(MailConst.TYPE_EXCHANGE);
            emailModel.setFrom("<EMAIL>");
            emailModel.setFromName("DB Sender");
            emailModel.setTo(Arrays.asList("<EMAIL>"));
            emailModel.setSubject("Hi");
            emailModel.setContent("这是封发自Sedncloud的测试邮件");
            JSONObject object = new JSONObject();
            object.put("msg_type", MailServiceImpl.MSG_TYP_MAIL);
            object.put("data", emailModel );
            MqUtil.sendMailMq(object.toJSONString());
        } catch (MailSendException e) {
            System.out.println("test ik");
        }

    }

    @Test
    public void test_email() {
        String msg = "{\"attList\":[],\"bcc\":[],\"calendarUid\":\"20210516T1621135419456\",\"cc\":[\"<EMAIL>\",\"<EMAIL>\"],\"content\":\"<div style=\\\"font-size: 11pt; font-family: Calibri;\\\">\\n<div style=\\\"font-size: 11pt; font-family: Calibri;\\\">\\n<div><strong>Capvision Conference Line</strong></div>\\n<ul style=\\\"margin: 0;\\\">\\n<li>Dial-in details to be shared after approval</li>\\n</ul>\\n<div> </div>\\n<div>\\n<div style=\\\"font-size: 11pt; font-family: Calibri;\\\" data-v-3db3acfd=\\\"\\\">\\n<div data-v-3db3acfd=\\\"\\\"><strong style=\\\"font-size: 14pt;\\\" data-v-3db3acfd=\\\"\\\"> Ping Li</strong> <em data-v-3db3acfd=\\\"\\\"> – Alabama</em></div>\\n<div data-v-3db3acfd=\\\"\\\"><strong data-v-3db3acfd=\\\"\\\">Nominal Charge Rate: $300 per hour. </strong></div>\\n<div data-v-3db3acfd=\\\"\\\"><strong data-v-3db3acfd=\\\"\\\"> Additional Charges: <span data-v-3db3acfd=\\\"\\\">This advisor is 9x Standard Rate.</span> </strong></div>\\n<div style=\\\"margin-bottom: 20px;\\\" data-v-3db3acfd=\\\"\\\">\\n<div style=\\\"margin-bottom: 10px;\\\" data-v-3db3acfd=\\\"\\\">\\n<p style=\\\"margin-top: 0px; margin-bottom: 0px;\\\" data-v-3db3acfd=\\\"\\\">kelsiy | 互联网行业 猎头顾问 | Oct 2014 – Jun 2020 Private</p>\\n<p style=\\\"margin-top: 0px; margin-bottom: 0px;\\\" data-v-3db3acfd=\\\"\\\">展动力人才资讯（中国）有限公司 | 互联网行业 猎头顾问 | May 2013 – Oct 2014 Private</p>\\n<p style=\\\"margin-top: 0px; margin-bottom: 0px;\\\" data-v-3db3acfd=\\\"\\\">方正国际 | 打杂 | Feb 2011 – May 2013 Private</p>\\n<p style=\\\"margin-top: 0px; margin-bottom: 0px;\\\" data-v-3db3acfd=\\\"\\\">Apple Inc. | COO | Jan 2021 – Current |Buenos Aires Stock Exchange - AAPL</p>\\n</div>\\n</div>\\n<p style=\\\"margin-top: 2px; margin-bottom: 0px;\\\" data-v-3db3acfd=\\\"\\\"><strong data-v-3db3acfd=\\\"\\\">Availability<span data-v-3db3acfd=\\\"\\\">(CST)</span></strong></p>\\n<div data-v-3db3acfd=\\\"\\\">\\n<p style=\\\"margin-top: 0px; margin-bottom: 0px;\\\" data-v-3db3acfd=\\\"\\\">- Sunday 16/5: 10:00 AM - 10:30 AM</p>\\n<p style=\\\"margin-top: 0px; margin-bottom: 0px;\\\" data-v-3db3acfd=\\\"\\\">- Sunday 16/5: 12:00 PM - 12:30 PM</p>\\n</div>\\n</div>\\n</div>\\n<div> </div>\\n<div><strong>Screening Questions/Comments</strong></div>\\n<div>\\n<div contenteditable=\\\"false\\\">\\n<div style=\\\"margin: 0px; padding-left: 30px;\\\" data-v-14ead5de=\\\"\\\">\\n<div style=\\\"font-size: 11pt; font-family: Calibri;\\\" data-v-14ead5de=\\\"\\\">\\n<div data-v-14ead5de=\\\"\\\"><span data-v-14ead5de=\\\"\\\">1. </span> 唐太宗是谁？</div>\\n<divstyle=\\\"color: #6ea1d0; padding-left: 15px;\\\" data-v-14ead5de=\\\"\\\"><em data-v-14ead5de=\\\"\\\">李世民</em></div>\\n</div>\\n</div>\\n</div>\\n</div>\\n<div> </div>\\n<div>\\n<div>Best,</div>\\n<div>\\n<div class=\\\"WordSection1\\\">\\n<p class=\\\"MsoNormal\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 18.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision</span></strong><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 16.0pt; color: #1f497d; mso-no-proof: yes;\\\"> Partners</span></strong></span></p>\\n</div>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">Shanghai |Hong Kong |Beijing | New York |Shenzhen</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; font-family: 'Tahoma','sans-serif'; color: #999999; mso-no-proof: yes;\\\"> </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">|Berlin</span></strong></span></p>\\n</div>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Ping Li </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">李平</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> | </span></strong></span></p>\\n</div>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision Partners Co., Ltd.</span></strong></span></p>\\n</div>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Room 801-803, Greentech Tower, No.436, Hengfeng Road, Zhabei District, Shanghai 200070, PRC</span></span></p>\\n</div>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">中国上海市闸北区恒丰路</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">436</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">号环智国际大厦</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> 801-803</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family:宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">室（邮编</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">  200070</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">）</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Tel: (86) 21-6056-1968 Ext: | Mobile: (86) 18221195569 | Fax</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">：</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">(86)21- 3251-1247</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span lang=\\\"EN-US\\\"> </span></p>\",\"endTime\":\"2021-05-16T04:30:00.000+0000\",\"from\":\"<EMAIL>\",\"fromName\":\"DB Sender\",\"location\":\"\",\"sequence\":1,\"startTime\":\"2021-05-16T04:00:00.000+0000\",\"subject\":\"Pending Compliance Approval – Capvision Consultation | 大唐帝国影视投资项目: Ping Li\",\"to\":[\"<EMAIL>\"],\"type\":\"002\"}";

        EmailModel model = JSONObject.parseObject(msg, EmailModel.class);
        System.out.println(model.getStartTime());
        model.setStartTime(DateUtil.unix_time_to_date(DateUtil.date_to_unix_time(model.getStartTime(), TimeZone.getTimeZone("UTC").toZoneId())));
        System.out.println(model.getStartTime());

        LocalDateTime localDateTime = model.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        System.out.println(localDateTime);
//        Date date = new Date(1621342800000L);
//        System.out.println(date);
//        localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        System.out.println(ZoneId.systemDefault());
        System.out.println(TimeZone.getTimeZone("Asia/Shanghai").toZoneId());
        System.out.println(localDateTime);
//        LocalDate.
//        DateUtil.date_to_local_date()
//        DateUtil.date_str(DateUtil.date_str(model.getStartTime(), DateUtil.SDF_HMS), DateUtil.SDF_HMS);
        System.out.println(ZonedDateTime.of(localDateTime, TimeZone.getTimeZone("UTC").toZoneId()).toLocalDateTime());
        ZoneId zone;
        Date s = model.getStartTime();
        ZonedDateTime zonedDateTime = ZonedDateTime.of(localDateTime.getYear(),localDateTime.getMonthValue() ,localDateTime.getDayOfMonth() , localDateTime.getHour() ,localDateTime.getMinute() , localDateTime.getSecond(), localDateTime.getNano(), TimeZone.getTimeZone("UTC").toZoneId());
        System.out.println(zonedDateTime);
        System.out.println(zonedDateTime.toEpochSecond());
        System.out.println(ZonedDateTime.of(zonedDateTime.toLocalDateTime(), ZoneId.systemDefault()).toEpochSecond());

    }

//    @Test
//    public void test_calendar() {
//        EmailModel model = JSONObject.parseObject("{\"cc\":[],\"bcc\":[],\"subject\":\"test项目来源NPT - Arrange Consultation - Capvision 12-\",\"calendarUid\":\"20190110T15471259986390\",\"type\":\"002\",\"content\":\"<strong>Consultant ID:</strong> I<br />\\n\\t\\t\\t<strong>Consultant Name:</strong> Simon2<br />\\n\\t\\t\\t<strong>Date:</strong> Friday 5th of July 2019<br />\\n\\t\\t\\t<strong>Start Time:</strong> 18:00 Beijing Time<br />\\n\\t\\t\\t<strong>Type of Interview:</strong> In-Person<br />\\n\\t\\t\\t<strong>Interview Information:</strong> Contact Number: 852-1234578,86-15651640765<br />\\n\\t\\t\\tAddress:<br />\\n\\t\\t\\t<strong>Company:</strong> 上海长征医院南京分院(Current)<br />\\n\\t\\t\\t<strong>Position:</strong> SDF(Current)<br />\\n\\t\\t\\t<strong>Background:</strong> 顾问2009年至今在河南圣光任总经理，负责公司运营和管理。顾问2009年至今在河南圣光任总经理，负责公司运营和管理。顾问2009年至今在河南圣光任总经理，负责公司运营和管理。test.顾问2009年至今在河南圣光任总经理，负责公司运营和管理。<br />\\n\\t\\t\\t<strong>Expertise:</strong> 负责公司运营和管理。负责公司运营和管理。，负责公司运营和管理。<br />\\n\\t\\t\\t&nbsp;\",\"sequence\":10,\"fromName\":\"Ping Li\",\"from\":\"<EMAIL>\",\"location\":\"\",\"startTime\":1621389600000,\"endTime\":1621393200000,\"to\":[\"<EMAIL>\", \"<EMAIL>\"],\"optional_to\":[]}", EmailModel.class);
//
//        model.setSequence(30);
//        model.setStartTime(com.cpvsn.web.cm.core.util.DateUtil.str_to_datetime("2022-07-21 09:00", com.cpvsn.web.cm.core.util.DateUtil.SDF_HM));
//        model.setEndTime(com.cpvsn.web.cm.core.util.DateUtil.str_to_datetime("2022-07-21 09:45", com.cpvsn.web.cm.core.util.DateUtil.SDF_HM));
//        model.setCalendarUid("23432-45445646s234564596");
//
//        try {
//            mailService.sendCalendar(model);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    @Test
    public void testPostMarkWithCustomArgs() throws Exception {
        String str = "{\"cc\":[],\"extra_params\":{},\"bcc\":[],\"subject\":\"Capvision Registration\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"172113522525488_985862_CAPVISION-rm-web$<EMAIL>>\",\"type\":\"002\",\"content\":\"<div><span style=\\\"font-family: Calibri; font-size: 11pt;\\\">Hi Ping,</span></div>\\n<div>\\n<div><br /><span style=\\\"font-family: Calibri;\\\">Thank you for your interest in joining the Capvision Expert Network. The next step is to become an advisor in Capvision’s network. This process includes accepting our (1) Terms &amp; Conditions via the link below, and (2) Compliance Overview attached.</span></div>\\n<div><br /><span style=\\\"font-family: Calibri; font-size: 11pt;\\\"><a href=\\\"https://qa-udb2.capvision.com/portal/#/b/AP1DI5JQAW3C7\\\" target=\\\"_blank\\\" rel=\\\"noopener\\\">Capvision Terms &amp; Conditions</a></span><br /><br /><span style=\\\"font-family: Calibri; font-size: 11pt;\\\">After you accept our Terms &amp; Conditions, we will proceed with submitting your information to our client. If they would like to move forward with scheduling a consultation with you, I will reach back out regarding next steps. If not, I will follow up to inform you and we will keep your profile in our network for any future consultations that fit your background and expertise.</span></div>\\n<div><span style=\\\"font-family: Calibri; font-size: 11pt;\\\"> </span><br /><span style=\\\"font-family: Calibri; font-size: 11pt;\\\">Best,</span></div>\\n<div>\\n<div class=\\\"WordSection1\\\">\\n<p class=\\\"MsoNormal\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 18.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision</span></strong><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 16.0pt; color: #1f497d; mso-no-proof: yes;\\\"> Partners</span></strong></span></p>\\n</div>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">Shanghai |Hong Kong |Beijing | New York |Shenzhen</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; font-family: 'Tahoma','sans-serif'; color: #999999; mso-no-proof: yes;\\\"> </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">|Berlin</span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Ping Li </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">李平</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> | </span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision Partners Co., Ltd.</span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Room 801-803, Greentech Tower, No.436, Hengfeng Road, Zhabei District, Shanghai 200070, PRC</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">中国上海市闸北区恒丰路</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">436</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">号环智国际大厦</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> 801-803</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">室（邮编</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">  200070</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">）</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Tel: (86) 21-6056-1968 Ext: | Mobile: (86) 18221195569 | Fax</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">：</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">(86)21- 3251-1247</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span lang=\\\"EN-US\\\"> </span></p>\\n</div>\\n</div>\",\"sequence\":0,\"fromName\":\"Ping Li\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{}}";
        EmailModel model = JSONObject.parseObject(str, EmailModel.class);
        System.out.println(model);
        mailService.filterEmailModel(model);
//        model.setContent("<div style=\\\"font-size: 11pt; font-family: Calibri;\\\">Hi 文轩2,<br /><br />I'm working with a client who is hoping to learn more about test display project. Given your experience, I thought you'd be a good person to reach out to for a brief conversation.<br /><br />Our end goal would be to schedule a ~45-60 minute phone consultation between an expert such as yourself and my client, for which we are of course happy to compensate.<br /><br />Do you have a 3-5 minutes today to briefly discuss the request? Please let me know the best time and number to reach you or feel free to call me at your convenience at 86-18221195569.<br /><br />Best,<br /><div class=\\\"WordSection1\\\">\\n<p class=\\\"MsoNormal\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 18.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision</span></strong><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 16.0pt; color: #1f497d; mso-no-proof: yes;\\\"> Partners</span></strong></span></p>\\n</div>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">Shanghai |Hong Kong |Beijing | New York |Shenzhen</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; font-family: 'Tahoma','sans-serif'; color: #999999; mso-no-proof: yes;\\\"> </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">|Berlin</span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Ping Li </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">李平</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> | </span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision Partners Co., Ltd.</span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Room 801-803, Greentech Tower, No.436, Hengfeng Road, Zhabei District, Shanghai 200070, PRC</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">中国上海市闸北区恒丰路</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">436</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">号环智国际大厦</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> 801-803</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">室（邮编</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">  200070</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">）</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Tel: (86) 21-6056-1968 Ext: | Mobile: (86) 18221195569 | Fax</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">：</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">(86)21- 3251-1247</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span lang=\\\"EN-US\\\"> </span></p> | Research Manager<br />135 E 57th St, 8th Floor | New York, NY 10022<br />p: 86-18221195569 e: <EMAIL></div>");
        model.setFrom("<EMAIL>");
        model.setFromName("Ping whatever");
        model.setTo(Arrays.asList("<EMAIL>", "<EMAIL>", "<EMAIL>"));
        Map<String, Object> map = new HashMap<>();
        map.put(postMarkService.POSTMARK_CUSTOM_ARG_PREFIX + "email_record_id", "1");
        model.setExtra_params(map);
        model.setReply_to_list(Collections.singletonList("<EMAIL>"));
        model.setReply_message_id("1711352255488_985862_CAPVISION-rm-web$<EMAIL>");

        postMarkService.sendMail(model);
    }

    @Test
    public void testResendSendEmail() throws Exception {
        String str = "{\"cc\":[],\"extra_params\":{},\"bcc\":[],\"subject\":\"Capvision Registration\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"172113522525488_985862_CAPVISION-rm-web$<EMAIL>>\",\"type\":\"002\",\"content\":\"<div><span style=\\\"font-family: Calibri; font-size: 11pt;\\\">Hi Ping,</span></div>\\n<div>\\n<div><br /><span style=\\\"font-family: Calibri;\\\">Thank you for your interest in joining the Capvision Expert Network. The next step is to become an advisor in Capvision’s network. This process includes accepting our (1) Terms &amp; Conditions via the link below, and (2) Compliance Overview attached.</span></div>\\n<div><br /><span style=\\\"font-family: Calibri; font-size: 11pt;\\\"><a href=\\\"https://qa-udb2.capvision.com/portal/#/b/AP1DI5JQAW3C7\\\" target=\\\"_blank\\\" rel=\\\"noopener\\\">Capvision Terms &amp; Conditions</a></span><br /><br /><span style=\\\"font-family: Calibri; font-size: 11pt;\\\">After you accept our Terms &amp; Conditions, we will proceed with submitting your information to our client. If they would like to move forward with scheduling a consultation with you, I will reach back out regarding next steps. If not, I will follow up to inform you and we will keep your profile in our network for any future consultations that fit your background and expertise.</span></div>\\n<div><span style=\\\"font-family: Calibri; font-size: 11pt;\\\"> </span><br /><span style=\\\"font-family: Calibri; font-size: 11pt;\\\">Best,</span></div>\\n<div>\\n<div class=\\\"WordSection1\\\">\\n<p class=\\\"MsoNormal\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 18.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision</span></strong><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 16.0pt; color: #1f497d; mso-no-proof: yes;\\\"> Partners</span></strong></span></p>\\n</div>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">Shanghai |Hong Kong |Beijing | New York |Shenzhen</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; font-family: 'Tahoma','sans-serif'; color: #999999; mso-no-proof: yes;\\\"> </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">|Berlin</span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Ping Li </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">李平</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> | </span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision Partners Co., Ltd.</span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Room 801-803, Greentech Tower, No.436, Hengfeng Road, Zhabei District, Shanghai 200070, PRC</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">中国上海市闸北区恒丰路</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">436</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">号环智国际大厦</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> 801-803</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">室（邮编</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">  200070</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">）</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Tel: (86) 21-6056-1968 Ext: | Mobile: (86) 18221195569 | Fax</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">：</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">(86)21- 3251-1247</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span lang=\\\"EN-US\\\"> </span></p>\\n</div>\\n</div>\",\"sequence\":0,\"fromName\":\"Ping Li\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{}}";
        EmailModel model = JSONObject.parseObject(str, EmailModel.class);
        System.out.println(model);
        mailService.filterEmailModel(model);
        model.setFrom("<EMAIL>");
        model.setFromName("Ping whatever");
        model.setTo(Arrays.asList("<EMAIL>", "<EMAIL>", "<EMAIL>"));
        Map<String, Object> map = new HashMap<>();
        map.put(resendService.RESEND_CUSTOM_ARG_PREFIX + "email_record_id", "1");
        model.setExtra_params(map);
        model.setReply_to_list(Collections.singletonList("<EMAIL>"));
        model.setReply_message_id("1711352255488_985862_CAPVISION-rm-web$<EMAIL>");
        EmailAttModel attModel = new EmailAttModel();
        attModel.setFilePath("https://qa-udb2.capvision.com/uploader/download/articles/2024-08/2d78bb97c08e496cc708d4b17eaff2c1.pdf");
        attModel.setAttName("Capvision Expert Compliance Snapshot.pdf");
        model.setAttList(Collections.singletonList(attModel));

        resendService.sendEmail(model);
    }

    @Test
    public void testResendSendBatchEmail() throws Exception {
        String str = "{\"cc\":[],\"extra_params\":{},\"bcc\":[],\"subject\":\"Capvision Registration\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"172113522525488_985862_CAPVISION-rm-web$<EMAIL>>\",\"type\":\"002\",\"content\":\"<div><span style=\\\"font-family: Calibri; font-size: 11pt;\\\">Hi Ping,</span></div>\\n<div>\\n<div><br /><span style=\\\"font-family: Calibri;\\\">Thank you for your interest in joining the Capvision Expert Network. The next step is to become an advisor in Capvision’s network. This process includes accepting our (1) Terms &amp; Conditions via the link below, and (2) Compliance Overview attached.</span></div>\\n<div><br /><span style=\\\"font-family: Calibri; font-size: 11pt;\\\"><a href=\\\"https://qa-udb2.capvision.com/portal/#/b/AP1DI5JQAW3C7\\\" target=\\\"_blank\\\" rel=\\\"noopener\\\">Capvision Terms &amp; Conditions</a></span><br /><br /><span style=\\\"font-family: Calibri; font-size: 11pt;\\\">After you accept our Terms &amp; Conditions, we will proceed with submitting your information to our client. If they would like to move forward with scheduling a consultation with you, I will reach back out regarding next steps. If not, I will follow up to inform you and we will keep your profile in our network for any future consultations that fit your background and expertise.</span></div>\\n<div><span style=\\\"font-family: Calibri; font-size: 11pt;\\\"> </span><br /><span style=\\\"font-family: Calibri; font-size: 11pt;\\\">Best,</span></div>\\n<div>\\n<div class=\\\"WordSection1\\\">\\n<p class=\\\"MsoNormal\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 18.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision</span></strong><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 16.0pt; color: #1f497d; mso-no-proof: yes;\\\"> Partners</span></strong></span></p>\\n</div>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">Shanghai |Hong Kong |Beijing | New York |Shenzhen</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; font-family: 'Tahoma','sans-serif'; color: #999999; mso-no-proof: yes;\\\"> </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">|Berlin</span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Ping Li </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">李平</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> | </span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision Partners Co., Ltd.</span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Room 801-803, Greentech Tower, No.436, Hengfeng Road, Zhabei District, Shanghai 200070, PRC</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">中国上海市闸北区恒丰路</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">436</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">号环智国际大厦</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> 801-803</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">室（邮编</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">  200070</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">）</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Tel: (86) 21-6056-1968 Ext: | Mobile: (86) 18221195569 | Fax</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">：</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">(86)21- 3251-1247</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span lang=\\\"EN-US\\\"> </span></p>\\n</div>\\n</div>\",\"sequence\":0,\"fromName\":\"Ping Li\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{}}";
        EmailModel model = JSONObject.parseObject(str, EmailModel.class);
        System.out.println(model);
        mailService.filterEmailModel(model);
        model.setFrom("<EMAIL>");
        model.setFromName("Ping whatever");
        model.setTo(Arrays.asList("<EMAIL>", "<EMAIL>", "<EMAIL>"));
        Map<String, Object> map = new HashMap<>();
        map.put(resendService.RESEND_CUSTOM_ARG_PREFIX + "email_record_id", "1");
        model.setExtra_params(map);
        model.setReply_to_list(Collections.singletonList("<EMAIL>"));
        model.setReply_message_id("1711352255488_985862_CAPVISION-rm-web$<EMAIL>");
        EmailAttModel attModel = new EmailAttModel();
        attModel.setFilePath("https://qa-udb2.capvision.com/uploader/download/tc_attachment/2024-08/b0edcb6e1192cb2ff89a770906ce7361");
        attModel.setAttName("Capvision Expert Compliance Snapshot.pdf");
        model.setAttList(Collections.singletonList(attModel));


        EmailModel model2 = JSONObject.parseObject(JSONObject.toJSONString(model), EmailModel.class);
        model2.setReply_message_id("1711352255488_9858623_CAPVISION-rm-web$<EMAIL>");
        model2.setFromName("Ping Li");

        EmailModel model3 = JSONObject.parseObject(JSONObject.toJSONString(model), EmailModel.class);
        model3.setReply_message_id("1711352255488_98586s23_CAPVISION-rm-web$<EMAIL>");
        model3.setFromName("Ping Li");

        resendService.sendBatchEmail(Arrays.asList(model, model2, model3));
    }


    @Test
    public void testResendBatchEmailnew() throws Exception {
        String str = "{\"email_model_list\":[{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30507,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30507,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_897965_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi Kylie, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPSQWCIKZ</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADEC4WCIEB</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUOEWCIEA\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUOEWCIEA>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30508,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30508,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_524963_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi Sarah, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPSCWCIEC</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADEC5WCIE2</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUOEWCIED\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUOEWCIED>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30509,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30509,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_703079_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi 平, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPSQWCIE3</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADECFWCIE5</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUOEWCIE4\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUOEWCIE4>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30510,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30510,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_036898_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi Fai, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPS7WCIE6</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADEC8WCIE8</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUOPWCIE7\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUOPWCIE7>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30511,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30511,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_577511_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi Wenxuan, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPSTWCIE9</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADEC5WCIEJ</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUO3WCIEH\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUO3WCIEH>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30512,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30512,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_805526_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi Yuchong (Frank), great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPSTWCIEK</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADECEWCIEF</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUO9WCIEE\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUO9WCIEE>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30513,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30513,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_111737_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi 文轩, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPSMWCIEI</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADECVWCIEN</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUOBWCIEM\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUOBWCIEM>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30514,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30514,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_190674_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi Carlos, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPSFWCIEP</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADECEWCIEU</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUOSWCIEQ\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUOSWCIEQ>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30515,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30515,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_155312_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi Erle, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPSKWCIEV</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADECDWCIES</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUO9WCIER\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUO9WCIER>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30516,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30516,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_636870_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi Ronald, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPS2WCIET</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADECXWCIEY</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUOJWCIEX\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUOJWCIEX>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30517,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30517,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_207501_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi Tim, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPSVWCIEZ</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADECPWCIFB</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUOSWCIFA\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUOSWCIFA>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30518,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30518,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_185635_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi Gabriel, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPS8WCIFC</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADECNWCIF2</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUOQWCIFD\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUOQWCIFD>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30519,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30519,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_403962_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi Satchit, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPSCWCIF3</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADECQWCIF5</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUOCWCIF4\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUOCWCIF4>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30520,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30520,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_120185_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi Fabio, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPS5WCIF6</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADECZWCIF8</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUONWCIF7\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUONWCIF7>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30521,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30521,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_664990_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi Allison, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPSXWCIF9</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADEC3WCIFJ</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUOBWCIFH\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUOBWCIFH>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30522,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30522,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_132091_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi Jeremie, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPSTWCIFK</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADECSWCIFF</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUOJWCIFE\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUOJWCIFE>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30523,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30523,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_111951_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi Will, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPSFWCIFI</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADECYWCIFN</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUOAWCIFM\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUOAWCIFM>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30524,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30524,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_417018_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi Will, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPSDWCIFP</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADECNWCIFU</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUO8WCIFQ\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUO8WCIFQ>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30525,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30525,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_668997_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi Ralph, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPS6WCIFV</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADECJWCIFS</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUODWCIFR\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUODWCIFR>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30526,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30526,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_723461_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi ceshiren, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPSRWCIFT</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADECNWCIFY</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUOBWCIFX\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUOBWCIFX>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30527,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30527,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_287738_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi PIngli, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPS7WCIFZ</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADECTWCIIB</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUOBWCIIA\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUOBWCIIA>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30528,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30528,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_351547_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi Leighton, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPSZWCIIC</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADECUWCII2</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUOVWCIID\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUOVWCIID>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30529,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30529,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_815624_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi Drew, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPS3WCII3</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADEC6WCII5</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少��� 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUOCWCII4\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUOCWCII4>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30530,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30530,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_594882_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi Cameron, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPS6WCII6</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADEC9WCII8</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUOIWCII7\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUOIWCII7>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30531,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30531,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_221046_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi Ping, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPSDWCII9</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADECQWCIIJ</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUOIWCIIH\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUOIWCIIH>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30532,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30532,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_240252_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi mike, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPSXWCIIK</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADEC9WCIIF</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUO8WCIIE\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUO8WCIIE>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30533,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30533,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_566204_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi mike, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPS7WCIII</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADECZWCIIN</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUO2WCIIM\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUO2WCIIM>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30534,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30534,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_479515_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi displayid, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPSTWCIIP</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADECCWCIIU</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUORWCIIQ\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUORWCIIQ>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30535,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30535,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_403193_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi displayid, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPS4WCIIV</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADECHWCIIS</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUOUWCIIR\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUOUWCIIR>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30536,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30536,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_845316_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi mike, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPS6WCIIT</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADECKWCIIY</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUOBWCIIX\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUOBWCIIX>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30537,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30537,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_537121_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi test, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPSFWCIIZ</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADECTWCIMB</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUOMWCIMA\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUOMWCIMA>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30538,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30538,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_871538_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi mike_cn_2, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPSTWCIMC</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADECXWCIM2</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUOKWCIMD\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUOKWCIMD>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30539,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30539,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_431775_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi NICHOLAS, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPSNWCIM3</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADEC4WCIM5</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUO7WCIM4\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUO7WCIM4>\"}},{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":30540,\"POSTMARK_CUSTOM_ARGS_email_record_id\":30540,\"is_outreach\":true,\"POSTMARK_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\",\"SG_CUSTOM_ARGS_env\":\"US-DEVELOPMENT\"},\"bcc\":[\"<EMAIL>\"],\"subject\":\"Capvision Survey - fgh\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"1724210076743_837096_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div>Hi AAA, great to connect! I am with Capvision, an expert network that connects professionals with experts like yourself. We have a project that requires a very specific profile and believe you are a good fit. We are looking for experts who (topic) to take part in a 12 minute survey. The survey is completely anonymous, and we do not want you to share any identifying information about yourself or your employer in the survey.</div>\\n<div>If you are interested in more information, please click the link below.</div>\\n<div>Take Survey: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/ATPSTWCIM6</div>\\n<div>Decline Project: https://qa-udb2.capvision.com/portal/#/advisor/decline/ADECPWCIM8</div>\\n<div>Honorarium upon completion: . Please note, this request is time sensitive. If you're not interested in participating in this survey, you may decline at any time.</div>\\n<div>Best, 少蒙 任</div><br><a href=\\\"https://www.capvision.com/#/privacy_policy\\\">Privacy Policy</a> | If you no longer would like to receive emails from Capvision, please <a href=\\\"https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/AUOBWCIM7\\\">unsubscribe here</a>.\",\"sequence\":0,\"prefer_vendor\":\"RESEND\",\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{\"List-Unsubscribe\":\"<https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach/AUOBWCIM7>\"}}],\"prefer_vendor\":\"RESEND\"}";

        BatchEmailModel emailModel = JSONObject.parseObject(str, BatchEmailModel.class);
        mailService.sendBatchMail(emailModel);
    }

    @Test
    public void testMailgunEmail() throws Exception {
        String str = "{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":11505158,\"POSTMARK_CUSTOM_ARGS_email_record_id\":11505158,\"MAILGUN_CUSTOM_ARGS_email_record_id\":11505158,\"is_outreach\":true,\"MAILGUN_CUSTOM_ARGS_env\":\"US-PRODUCTION\",\"RESEND_CUSTOM_ARGS_env\":\"US-PRODUCTION\",\"email_tag\":\"Amethyst,Sapphire,US Research Team\",\"SG_CUSTOM_ARGS_env\":\"US-PRODUCTION\"},\"bcc\":[],\"subject\":\"Capvision Registration\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"172113522525488_985862_CAPVISION-rm-web$<EMAIL>>\",\"type\":\"002\",\"content\":\"<div><span style=\\\"font-family: Calibri; font-size: 11pt;\\\">Hi Ping,</span></div>\\n<div>\\n<div><br /><span style=\\\"font-family: Calibri;\\\">Thank you for your interest in joining the Capvision Expert Network. The next step is to become an advisor in Capvision’s network. This process includes accepting our (1) Terms &amp; Conditions via the link below, and (2) Compliance Overview attached.</span></div>\\n<div><br /><span style=\\\"font-family: Calibri; font-size: 11pt;\\\"><a href=\\\"https://qa-udb2.capvision.com/portal/#/b/AP1DI5JQAW3C7\\\" target=\\\"_blank\\\" rel=\\\"noopener\\\">Capvision Terms &amp; Conditions</a></span><br /><br /><span style=\\\"font-family: Calibri; font-size: 11pt;\\\">After you accept our Terms &amp; Conditions, we will proceed with submitting your information to our client. If they would like to move forward with scheduling a consultation with you, I will reach back out regarding next steps. If not, I will follow up to inform you and we will keep your profile in our network for any future consultations that fit your background and expertise.</span></div>\\n<div><span style=\\\"font-family: Calibri; font-size: 11pt;\\\"> </span><br /><span style=\\\"font-family: Calibri; font-size: 11pt;\\\">Best,</span></div>\\n<div>\\n<div class=\\\"WordSection1\\\">\\n<p class=\\\"MsoNormal\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 18.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision</span></strong><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 16.0pt; color: #1f497d; mso-no-proof: yes;\\\"> Partners</span></strong></span></p>\\n</div>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">Shanghai |Hong Kong |Beijing | New York |Shenzhen</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; font-family: 'Tahoma','sans-serif'; color: #999999; mso-no-proof: yes;\\\"> </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">|Berlin</span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Ping Li </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">李平</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> | </span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision Partners Co., Ltd.</span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Room 801-803, Greentech Tower, No.436, Hengfeng Road, Zhabei District, Shanghai 200070, PRC</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">中国上海市闸北区恒丰路</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">436</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">号环智国际大厦</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> 801-803</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">室（邮编</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">  200070</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">）</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Tel: (86) 21-6056-1968 Ext: | Mobile: (86) 18221195569 | Fax</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">：</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">(86)21- 3251-1247</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span lang=\\\"EN-US\\\"> </span></p>\\n</div>\\n</div>\",\"sequence\":0,\"fromName\":\"Ping Li\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{}}";
        EmailModel model = JSONObject.parseObject(str, EmailModel.class);
        System.out.println(model);
        mailService.filterEmailModel(model);
//        model.setContent("<div style=\\\"font-size: 11pt; font-family: Calibri;\\\">Hi 文轩2,<br /><br />I'm working with a client who is hoping to learn more about test display project. Given your experience, I thought you'd be a good person to reach out to for a brief conversation.<br /><br />Our end goal would be to schedule a ~45-60 minute phone consultation between an expert such as yourself and my client, for which we are of course happy to compensate.<br /><br />Do you have a 3-5 minutes today to briefly discuss the request? Please let me know the best time and number to reach you or feel free to call me at your convenience at 86-18221195569.<br /><br />Best,<br /><div class=\\\"WordSection1\\\">\\n<p class=\\\"MsoNormal\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 18.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision</span></strong><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 16.0pt; color: #1f497d; mso-no-proof: yes;\\\"> Partners</span></strong></span></p>\\n</div>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">Shanghai |Hong Kong |Beijing | New York |Shenzhen</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; font-family: 'Tahoma','sans-serif'; color: #999999; mso-no-proof: yes;\\\"> </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">|Berlin</span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Ping Li </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">李平</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> | </span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision Partners Co., Ltd.</span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Room 801-803, Greentech Tower, No.436, Hengfeng Road, Zhabei District, Shanghai 200070, PRC</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">中国上海市闸北区恒丰路</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">436</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">号环智国际大厦</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> 801-803</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">室（邮编</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">  200070</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">）</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Tel: (86) 21-6056-1968 Ext: | Mobile: (86) 18221195569 | Fax</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">：</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">(86)21- 3251-1247</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span lang=\\\"EN-US\\\"> </span></p> | Research Manager<br />135 E 57th St, 8th Floor | New York, NY 10022<br />p: 86-18221195569 e: <EMAIL></div>");
        model.setFrom("<EMAIL>");
        model.setFromName("Ping whatever");
        model.setTo(Arrays.asList("<EMAIL>", "<EMAIL>", "<EMAIL>"));
//        model.setTo(new ArrayList<>());
        Map<String, Object> map = new HashMap<>();
//        map.put(MailgunService.MAILGUN_CUSTOM_ARG_PREFIX + "email_record_id", "1");
//        model.setExtra_params(map);
        model.setReply_to_list(Collections.singletonList("<EMAIL>"));
        model.setMessage_id("1711352255488_9858626_CAPVISION-rm-web$<EMAIL>");
        model.setReply_message_id("1711352255488_985862_CAPVISION-rm-web$<EMAIL>");

        mailgunService.sendMail(model);
    }

    @Test
    public void testMailjetEmail() throws Exception {
        String str = "{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":11505158,\"POSTMARK_CUSTOM_ARGS_email_record_id\":11505158,\"MAILJET_CUSTOM_ARGS_email_record_id\":11505158,\"is_outreach\":true,\"MAILJET_CUSTOM_ARGS_env\":\"US-PRODUCTION\",\"RESEND_CUSTOM_ARGS_env\":\"US-PRODUCTION\",\"email_tag\":\"Amethyst,Sapphire,US Research Team\",\"SG_CUSTOM_ARGS_env\":\"US-PRODUCTION\"},\"bcc\":[],\"subject\":\"Capvision Registration\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"172113522525488_985862_CAPVISION-rm-web$<EMAIL>>\",\"type\":\"002\",\"content\":\"<div><span style=\\\"font-family: Calibri; font-size: 11pt;\\\">Hi Ping,</span></div>\\n<div>\\n<div><br /><span style=\\\"font-family: Calibri;\\\">Thank you for your interest in joining the Capvision Expert Network. The next step is to become an advisor in Capvision’s network. This process includes accepting our (1) Terms &amp; Conditions via the link below, and (2) Compliance Overview attached.</span></div>\\n<div><br /><span style=\\\"font-family: Calibri; font-size: 11pt;\\\"><a href=\\\"https://qa-udb2.capvision.com/portal/#/b/AP1DI5JQAW3C7\\\" target=\\\"_blank\\\" rel=\\\"noopener\\\">Capvision Terms &amp; Conditions</a></span><br /><br /><span style=\\\"font-family: Calibri; font-size: 11pt;\\\">After you accept our Terms &amp; Conditions, we will proceed with submitting your information to our client. If they would like to move forward with scheduling a consultation with you, I will reach back out regarding next steps. If not, I will follow up to inform you and we will keep your profile in our network for any future consultations that fit your background and expertise.</span></div>\\n<div><span style=\\\"font-family: Calibri; font-size: 11pt;\\\"> </span><br /><span style=\\\"font-family: Calibri; font-size: 11pt;\\\">Best,</span></div>\\n<div>\\n<div class=\\\"WordSection1\\\">\\n<p class=\\\"MsoNormal\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 18.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision</span></strong><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 16.0pt; color: #1f497d; mso-no-proof: yes;\\\"> Partners</span></strong></span></p>\\n</div>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">Shanghai |Hong Kong |Beijing | New York |Shenzhen</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; font-family: 'Tahoma','sans-serif'; color: #999999; mso-no-proof: yes;\\\"> </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">|Berlin</span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Ping Li </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">李平</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> | </span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision Partners Co., Ltd.</span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Room 801-803, Greentech Tower, No.436, Hengfeng Road, Zhabei District, Shanghai 200070, PRC</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">中国上海市闸北区恒丰路</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">436</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">号环智国际大厦</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> 801-803</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">室（邮编</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">  200070</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">）</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Tel: (86) 21-6056-1968 Ext: | Mobile: (86) 18221195569 | Fax</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">：</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">(86)21- 3251-1247</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span lang=\\\"EN-US\\\"> </span></p>\\n</div>\\n</div>\",\"sequence\":0,\"fromName\":\"Ping Li\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{}}";
        EmailModel model = JSONObject.parseObject(str, EmailModel.class);
        System.out.println(model);
        mailService.filterEmailModel(model);
//        model.setContent("<div style=\\\"font-size: 11pt; font-family: Calibri;\\\">Hi 文轩2,<br /><br />I'm working with a client who is hoping to learn more about test display project. Given your experience, I thought you'd be a good person to reach out to for a brief conversation.<br /><br />Our end goal would be to schedule a ~45-60 minute phone consultation between an expert such as yourself and my client, for which we are of course happy to compensate.<br /><br />Do you have a 3-5 minutes today to briefly discuss the request? Please let me know the best time and number to reach you or feel free to call me at your convenience at 86-18221195569.<br /><br />Best,<br /><div class=\\\"WordSection1\\\">\\n<p class=\\\"MsoNormal\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 18.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision</span></strong><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 16.0pt; color: #1f497d; mso-no-proof: yes;\\\"> Partners</span></strong></span></p>\\n</div>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">Shanghai |Hong Kong |Beijing | New York |Shenzhen</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; font-family: 'Tahoma','sans-serif'; color: #999999; mso-no-proof: yes;\\\"> </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">|Berlin</span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Ping Li </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">李平</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> | </span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision Partners Co., Ltd.</span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Room 801-803, Greentech Tower, No.436, Hengfeng Road, Zhabei District, Shanghai 200070, PRC</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">中国上海市闸北区恒丰路</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">436</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">号环智国际大厦</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> 801-803</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">室（邮编</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">  200070</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">）</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Tel: (86) 21-6056-1968 Ext: | Mobile: (86) 18221195569 | Fax</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">：</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">(86)21- 3251-1247</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span lang=\\\"EN-US\\\"> </span></p> | Research Manager<br />135 E 57th St, 8th Floor | New York, NY 10022<br />p: 86-18221195569 e: <EMAIL></div>");
        model.setFrom("<EMAIL>");
        model.setFromName("Ping whatever");
        model.setTo(Arrays.asList("<EMAIL>", "<EMAIL>", "<EMAIL>"));
//        model.setTo(new ArrayList<>());
        Map<String, Object> map = new HashMap<>();
//        map.put(MailgunService.MAILGUN_CUSTOM_ARG_PREFIX + "email_record_id", "1");
//        model.setExtra_params(map);
        EmailAttModel attModel = new EmailAttModel();
        attModel.setFilePath("https://qa-udb2.capvision.com/uploader/download/tc_attachment/2024-08/b0edcb6e1192cb2ff89a770906ce7361");
        attModel.setAttName("Capvision Expert Compliance Snapshot.pdf");
        model.setAttList(Collections.singletonList(attModel));
        model.setReply_to_list(Collections.singletonList("<EMAIL>"));
        model.setMessage_id("1711352255488_985862_CAPVISION-rm-web$<EMAIL>");
        model.setReply_message_id("1711352255488_985862_CAPVISION-rm-web$<EMAIL>");
        mailService.filterEmailModel(model);

        mailjetService.sendMail(model);
    }

    @Test
    public void testGoogleApiSendEmail() throws Exception {
        String str = "{\"cc\":[],\"extra_params\":{\"SG_CUSTOM_ARGS_email_record_id\":11505158,\"POSTMARK_CUSTOM_ARGS_email_record_id\":11505158,\"MAILJET_CUSTOM_ARGS_email_record_id\":11505158,\"is_outreach\":true,\"MAILJET_CUSTOM_ARGS_env\":\"US-PRODUCTION\",\"RESEND_CUSTOM_ARGS_env\":\"US-PRODUCTION\",\"email_tag\":\"Amethyst,Sapphire,US Research Team\",\"SG_CUSTOM_ARGS_env\":\"US-PRODUCTION\"},\"bcc\":[],\"subject\":\"Capvision Registration\",\"attList\":[],\"calendarUid\":\"\",\"message_id\":\"172113522525488_985862_CAPVISION-rm-web$<EMAIL>>\",\"type\":\"002\",\"content\":\"<div><span style=\\\"font-family: Calibri; font-size: 11pt;\\\">Hi Ping,</span></div>\\n<div>\\n<div><br /><span style=\\\"font-family: Calibri;\\\">Thank you for your interest in joining the Capvision Expert Network. The next step is to become an advisor in Capvision’s network. This process includes accepting our (1) Terms &amp; Conditions via the link below, and (2) Compliance Overview attached.</span></div>\\n<div><br /><span style=\\\"font-family: Calibri; font-size: 11pt;\\\"><a href=\\\"https://qa-udb2.capvision.com/portal/#/b/AP1DI5JQAW3C7\\\" target=\\\"_blank\\\" rel=\\\"noopener\\\">Capvision Terms &amp; Conditions</a></span><br /><br /><span style=\\\"font-family: Calibri; font-size: 11pt;\\\">After you accept our Terms &amp; Conditions, we will proceed with submitting your information to our client. If they would like to move forward with scheduling a consultation with you, I will reach back out regarding next steps. If not, I will follow up to inform you and we will keep your profile in our network for any future consultations that fit your background and expertise.</span></div>\\n<div><span style=\\\"font-family: Calibri; font-size: 11pt;\\\"> </span><br /><span style=\\\"font-family: Calibri; font-size: 11pt;\\\">Best,</span></div>\\n<div>\\n<div class=\\\"WordSection1\\\">\\n<p class=\\\"MsoNormal\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 18.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision</span></strong><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 16.0pt; color: #1f497d; mso-no-proof: yes;\\\"> Partners</span></strong></span></p>\\n</div>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">Shanghai |Hong Kong |Beijing | New York |Shenzhen</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; font-family: 'Tahoma','sans-serif'; color: #999999; mso-no-proof: yes;\\\"> </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">|Berlin</span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Ping Li </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">李平</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> | </span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision Partners Co., Ltd.</span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Room 801-803, Greentech Tower, No.436, Hengfeng Road, Zhabei District, Shanghai 200070, PRC</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">中国上海市闸北区恒丰路</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">436</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">号环智国际大厦</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> 801-803</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">室（邮编</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">  200070</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">）</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Tel: (86) 21-6056-1968 Ext: | Mobile: (86) 18221195569 | Fax</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">：</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">(86)21- 3251-1247</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span lang=\\\"EN-US\\\"> </span></p>\\n</div>\\n</div>\",\"sequence\":0,\"fromName\":\"Ping Li\",\"from\":\"<EMAIL>\",\"location\":\"\",\"to\":[\"<EMAIL>\"],\"heads\":{}}";
        EmailModel model = JSONObject.parseObject(str, EmailModel.class);
        System.out.println(model);
        mailService.filterEmailModel(model);
//        model.setContent("<div style=\\\"font-size: 11pt; font-family: Calibri;\\\">Hi 文轩2,<br /><br />I'm working with a client who is hoping to learn more about test display project. Given your experience, I thought you'd be a good person to reach out to for a brief conversation.<br /><br />Our end goal would be to schedule a ~45-60 minute phone consultation between an expert such as yourself and my client, for which we are of course happy to compensate.<br /><br />Do you have a 3-5 minutes today to briefly discuss the request? Please let me know the best time and number to reach you or feel free to call me at your convenience at 86-18221195569.<br /><br />Best,<br /><div class=\\\"WordSection1\\\">\\n<p class=\\\"MsoNormal\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 18.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision</span></strong><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 16.0pt; color: #1f497d; mso-no-proof: yes;\\\"> Partners</span></strong></span></p>\\n</div>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">Shanghai |Hong Kong |Beijing | New York |Shenzhen</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; font-family: 'Tahoma','sans-serif'; color: #999999; mso-no-proof: yes;\\\"> </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 9.0pt; color: #878787; mso-no-proof: yes;\\\">|Berlin</span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Ping Li </span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">李平</span></strong></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> | </span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><strong><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Capvision Partners Co., Ltd.</span></strong></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Room 801-803, Greentech Tower, No.436, Hengfeng Road, Zhabei District, Shanghai 200070, PRC</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">中国上海市闸北区恒丰路</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">436</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">号环智国际大厦</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\"> 801-803</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">室（邮编</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">  200070</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">）</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">Tel: (86) 21-6056-1968 Ext: | Mobile: (86) 18221195569 | Fax</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span style=\\\"font-size: 8.0pt; font-family: 宋体; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; color: #1f497d; mso-no-proof: yes;\\\">：</span></span><span style=\\\"mso-bookmark: _MailAutoSig;\\\"><span lang=\\\"EN-US\\\" style=\\\"font-size: 8.0pt; color: #1f497d; mso-no-proof: yes;\\\">(86)21- 3251-1247</span></span></p>\\n<p class=\\\"MsoNormal\\\"><span lang=\\\"EN-US\\\"> </span></p> | Research Manager<br />135 E 57th St, 8th Floor | New York, NY 10022<br />p: 86-18221195569 e: <EMAIL></div>");
        model.setFrom("<EMAIL>\n");
        model.setFromName("IT");
        model.setTo(Arrays.asList("<EMAIL>", "<EMAIL>", "<EMAIL>"));
//        model.setTo(new ArrayList<>());
        Map<String, Object> map = new HashMap<>();
//        map.put(MailgunService.MAILGUN_CUSTOM_ARG_PREFIX + "email_record_id", "1");
//        model.setExtra_params(map);
        EmailAttModel attModel = new EmailAttModel();
        attModel.setFilePath("https://qa-udb2.capvision.com/uploader/download/tc_attachment/2024-08/b0edcb6e1192cb2ff89a770906ce7361");
        attModel.setAttName("Capvision Expert Compliance Snapshot.pdf");
        model.setAttList(Collections.singletonList(attModel));
//        model.setReply_to_list(Collections.singletonList("<EMAIL>"));
        model.setMessage_id("1111111ds11_CAPVISION-rm-web$<EMAIL>");
//        model.setReply_message_id("1711352255488_985862_CAPVISION-rm-web$<EMAIL>");
        mailService.filterEmailModel(model);

        GoogleApiSender.sendEmailWithAttachment(model);
    }

    @Test
    public void testGoogleApiSendCalendar() throws Exception {
        EmailModel model = JSONObject.parseObject("{\"cc\":[],\"extra_params\":{},\"bcc\":[],\"subject\":\"Capvision Consultation |  Damian Ennis | Managing Director, Concise Infrastructure Company [kk]\",\"attList\":[],\"calendarUid\":\"20250310T1741592655151\",\"message_id\":\"1741592655152_782857_CAPVISION-rm-web$<EMAIL>\",\"type\":\"002\",\"content\":\"<div style=\\\"font-size: 11pt; font-family: Calibri;\\\">\\n<div><br /></div>\\n</div>\\n<div><span style=\\\"font-family: Calibri;\\\">Hi team,</span></div>\\n<div> </div>\\n<div style=\\\"font-family: Calibri;\\\">The consultation with Damian Ennis has been confirmed for Tuesday, March 11 5:00 PM (Asia/Shanghai). The dial-in details are below, please dial in to the call using the conference line provided below. Feel free to let me know if you have any questions.<br /><br />One-Click Dial: <a href=\\\"tel:+16469313860,,86070016299#,,,,,,,,,*0768#\\\" target=\\\"_blank\\\" rel=\\\"noopener\\\">+16469313860,,86070016299#,,,,,,,,,*0768#</a><br />Meeting ID: 86070016299<br />Passcode: 0768<br /><br /></div>\\n<div><a href=\\\"https://qa-compliance2.capvision.com/client-portal/#/conf-dial/TCDCDWCRDT\\\" target=\\\"_blank\\\" rel=\\\"noopener\\\">OR CLICK HERE to join the conference room</a></div>\\n<div><br /></div>\\n<div><span style=\\\"font-family: Calibri; font-size: 11pt;\\\"></span><br /></div>\\n<div>Please find the expert’s profile below.</div>\\n<div style=\\\"font-size: 11pt; font-family: Calibri;\\\">\\n<div> </div>\\n<div>\\n<table border=\\\"0\\\" style=\\\"margin-bottom: 5px;\\\" class=\\\"tinymce-custom-table\\\">\\n<tbody>\\n<tr>\\n<td style=\\\"padding: 0cm;\\\">\\n<p style=\\\"font-size: 11pt; font-family: Calibri; margin: 0px 0px 0pt;\\\"><b class=\\\"advisor-display-name\\\" style=\\\"font-size: 14pt;\\\">1.08 Damian Ennis</b> <i> – United States</i></p>\\n</td>\\n</tr>\\n<tr>\\n<td style=\\\"padding: 0cm;\\\">\\n<p style=\\\"font-size: 11pt; font-family: Calibri; margin: 0px;\\\"><b>Nominal Charge Rate: $1073741825.5 per hour.</b></p>\\n</td>\\n</tr>\\n<tr>\\n<td style=\\\"padding: 0cm;\\\">\\n<p style=\\\"font-size: 11pt; font-family: Calibri; margin: 0px;\\\"><b>Employment History</b></p>\\n</td>\\n</tr>\\n<tr>\\n<td style=\\\"padding: 0cm;\\\">\\n<ul style=\\\"font-size: 11pt; font-family: Calibri; margin-top: 0px; margin-bottom: 10px; margin-block-end: 0px; margin-inline: 0px;\\\">\\n<li style=\\\"margin-top: 0px; margin-bottom: 0px;\\\"><span><b> Concise Infrastructure Company | Managing Director | Jun 2019 – Current </b></span></li>\\n</ul>\\n</td>\\n</tr>\\n<tr>\\n<td style=\\\"padding: 0cm;\\\">\\n<p style=\\\"font-size: 11pt; font-family: Calibri; margin: 0px;\\\"> </p>\\n</td>\\n</tr>\\n<tr>\\n<td style=\\\"padding: 0cm;\\\">\\n<p style=\\\"font-size: 11pt; font-family: Calibri; margin: 0px;\\\"><b>Background</b></p>\\n</td>\\n</tr>\\n<tr>\\n<td style=\\\"padding: 0cm;\\\">\\n<p style=\\\"font-size: 11pt; font-family: Calibri; margin: 0px;\\\"><span>Damian Ennis has over a decade of experience in the Australian, American, Asian, and European Construction industry as a Project manager for numerous multibillion dollar projects and as a senior consultant. In Jun 2019, the advisor joined Concise Infrastructure Company, a consulting service that has significant experience in Building / complex Infrastructure Projects in Australia / US / Europe - having worked on projects in excess of $30b. As the Managing Director, he helps clients in various aspects of their business Industry Insights (investors), Feasibility Studies, Business Cases, EOI / Tender Transaction Management, and Contract Management. From Sep 2013 – May 2019, the advisor worked for CPB Contractors. As the Design/ Process manager for Major Projects, he was responsible for winning and managing all aspects of the various multibillion dollar projects the company took on. They include; $5b WestConnex NM5 [D&amp;C Tender, Supp Tender], $3.9b Rozelle Interchange [D&amp;C Tender], $3b WestConnex M4-M5. [D&amp;C Tender], and $2.7b WestConnex M4E. [D&amp;C - EOI, Tender, Mod Tender, Detailed Design, &amp; CPS].</span></p>\\n</td>\\n</tr>\\n</tbody>\\n</table>\\n<br /><br /></div>\\n</div>\\n<div>\\n<div><span style=\\\"font-family: Calibri;\\\"><strong>Screening Questions/Comments</strong></span></div>\\n<div>\\n<div style=\\\"font-size: 11pt; font-family: Calibri;\\\">\\n<div contenteditable=\\\"false\\\">\\n<table style=\\\"margin: 0px;\\\" class=\\\"tinymce-custom-table\\\">\\n<tbody>\\n<tr>\\n<td style=\\\"padding: 0cm;\\\">\\n<table style=\\\"margin: 0px 0px 5px;\\\" class=\\\"tinymce-custom-table\\\">\\n<tbody>\\n<tr>\\n<td style=\\\"padding: 0cm;\\\">\\n<p style=\\\"font-size: 11pt; font-family: Calibri; margin: 0px; white-space: pre-wrap;\\\"><span>1.<span style=\\\"word-break: break-word;\\\"><span>电风扇地方</span></span></span></p>\\n</td>\\n</tr>\\n<tr>\\n<td style=\\\"padding: 0cm;\\\">\\n<div>\\n<p style=\\\"color: #6ea1d0; padding-left: 15px; font-size: 11pt; font-family: Calibri; margin: 0px;\\\"><i>sdfsd</i></p>\\n</div>\\n</td>\\n</tr>\\n</tbody>\\n</table>\\n</td>\\n</tr>\\n</tbody>\\n</table>\\n</div>\\n</div>\\n</div>\\n<div><br /></div>\\n</div>\\n<div style=\\\"font-size: 11pt; font-family: Calibri;\\\">\\n<div><span style=\\\"font-size: 11pt;\\\">Best,</span></div>\\n<div>\\n<div>\\n<div style=\\\"font-size: 11pt; font-family: Calibri;\\\">viky ren</div>\\n</div>\\n</div>\\n</div>\",\"sequence\":0,\"fromName\":\"少蒙 任\",\"from\":\"<EMAIL>\",\"startTime\":\"2025-03-11T09:00:00.000+00:00\",\"location\":\"One-Click Dial: +16469313860,,86070016299#,,,,,,,,,*0768#\",\"to\":[\"<EMAIL>\",\"<EMAIL>\"],\"endTime\":\"2025-03-11T09:45:00.000+00:00\",\"heads\":{}}", EmailModel.class);

        model.setSequence(0);
        model.setFrom("<EMAIL>");
        model.setFromName("Ping Li");
        model.setTo(Arrays.asList("<EMAIL>", "<EMAIL>", "<EMAIL>"));
        model.setStartTime(DateUtil.str_to_date("2025-03-12 10:45", DateUtil.SDF_HM));
        model.setEndTime(DateUtil.str_to_date("2025-03-12 10:55", DateUtil.SDF_HM));
        model.setCalendarUid("12313-rm-server");
        model.setLocation("shanghai");
        model.setContent("update" + DateUtil.date_str(new Date(), DateUtil.SDF_FULL_TIMEZONE) + model.getContent());

        GoogleApiSender.sendCalendar(model);
    }

    @Test
    public void testGoogleApiCancelCalendar() throws Exception {
        EmailModel model = new EmailModel();
        model.setFrom("<EMAIL>");
        model.setCalendarUid("12313-rm-server");

        GoogleApiSender.cancelCalendar(model);
    }
}
