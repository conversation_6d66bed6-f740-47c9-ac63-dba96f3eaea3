package com.cpvsn.mq.test;

import com.alibaba.fastjson.JSONObject;
import com.cpvsn.core.exception.BusinessException;
import com.cpvsn.core.util.DemoUtil;
import com.cpvsn.mq.common.util.HttpSend;
import org.apache.http.HttpEntity;
import org.apache.http.client.fluent.Form;
import org.apache.http.client.fluent.Request;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;

/**
 * Created by cosx on 2015/10/23.
 */
public class HttpTest {

    @Test
    public void testYunBaPost() {
        JSONObject params = new JSONObject();
        params.put("method", "publish");
        params.put("appkey", "5629a7fdbe17bc415cfbf4c9");
        params.put("seckey", "sec-eSuUDCz3fl69PnJGXlHsH7Q34I5Bgn1OgjmTJfnUtbdwFPxC");
        params.put("topic", "ks");
        params.put("msg", "publish");

        String result = HttpSend.postSend("http://rest.yunba.io:8080", params.toJSONString());
        System.out.println(result);
    }

    @Test
    public void testPostSendCloud() throws IOException {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost uploadFile = new HttpPost("http://api.sendcloud.net/apiv2/mail/send");
//        uploadFile = new HttpPost("http://75d0dae1.proxy.webhookapp.com");

        MultipartEntityBuilder builder = MultipartEntityBuilder.create();
        builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
        builder.addTextBody("apiUser", "batch_kmail");
        builder.addTextBody("apiKey", "ZJUYrwRdBre4");
        builder.addTextBody("from", "<EMAIL>");
        builder.addTextBody("to", "<EMAIL>;<EMAIL>");
        builder.addTextBody("subject", "Hi");
        builder.addPart("html", new StringBody("这是封发自Sedncloud的测试邮件", UTF_8));

        builder.addBinaryBody("attachments", new File("C:\\Users\\<USER>\\Pictures\\images.jpg"), ContentType.APPLICATION_OCTET_STREAM, "a.jpg");
        HttpEntity multipart = builder.build();

        uploadFile.setEntity(multipart);

        CloseableHttpResponse response = httpClient.execute(uploadFile);
        HttpEntity responseEntity = response.getEntity();

        String result = EntityUtils.toString(responseEntity);

        System.out.println(result);
    }

    private static Charset UTF_8 = Charset.forName("UTF-8");

    @Test
    public void testSend() throws IOException {
        Form form = Form.form();

        form.add("apiUser", "batch_kmail");
        form.add("apiKey", "ZJUYrwRdBre4");
        form.add("from", "<EMAIL>");
        form.add("to", "<EMAIL>");
        form.add("subject", "Hi");
        form.add("html", "这是封发自Sedncloud的测试邮件");

//        String result = Request.Post("http://api.sendcloud.net/apiv2/mail/send")
        String result = Request.Post("http://75d0dae1.proxy.webhookapp.com")
                .bodyForm(form.build(), UTF_8)
                .bodyFile(new File("C:\\Users\\<USER>\\Pictures\\images.jpg"), ContentType.APPLICATION_OCTET_STREAM)
                .execute()
                .returnContent().asString(UTF_8);

        System.out.println(result);
    }

    @Test
    public void testDemo() throws Exception {
        System.out.println(DemoUtil.test());
    }

    @Test
    public void testException() {
        System.out.println(test(1));
        System.out.println(test(2));
    }

    private int test(int i) {
        System.out.println("**********************************");
        try {
            System.out.println(i);
            if (i % 2 == 0) {
                throw new BusinessException("test");
            }
            i++ ;
        }catch(BusinessException e) {
            i++;
            System.out.println("catch BusinessException" + i);
            return i;
        }catch(Exception e) {
            i++;
            System.out.println("catch exception" + i);
            return i;
        }
        finally {
            i ++;
            System.out.println("finally " + i);
        }
        i ++ ;
        System.out.println("end" + i);
        return i;
    }
}
