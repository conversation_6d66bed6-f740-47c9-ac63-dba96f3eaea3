package com.cpvsn.mq.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cpvsn.mq.common.model.email.EmailModel;
import com.cpvsn.mq.util.DateUtil;
import net.fortuna.ical4j.data.CalendarOutputter;
import net.fortuna.ical4j.model.DateTime;
import net.fortuna.ical4j.model.TimeZoneRegistry;
import net.fortuna.ical4j.model.TimeZoneRegistryFactory;
import net.fortuna.ical4j.model.ValidationException;
import net.fortuna.ical4j.model.component.VEvent;
import net.fortuna.ical4j.model.component.VTimeZone;
import net.fortuna.ical4j.model.property.Method;
import net.fortuna.ical4j.model.property.ProdId;
import net.fortuna.ical4j.model.property.Version;
import org.junit.Test;

import java.io.FileOutputStream;
import java.io.IOException;
import java.time.Instant;
import java.time.ZoneId;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;


public class CalendarTimezoneTest {

    @Test
    public void test100() {
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Shanghai"));
        printDate();
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
        printDate();
        TimeZone.setDefault(TimeZone.getTimeZone("America/New_York"));
        printDate();
    }

    private void printDate() {
        System.out.println("Current TZ = " + ZoneId.systemDefault());
        Date now = new Date();
        System.out.println("now = " + now);
        System.out.println("now to instant = " + now.toInstant());

        long timestamp = DateUtil.date_to_unix_time(now, TimeZone.getTimeZone("UTC").toZoneId());
        System.out.println("timestamp = " + timestamp);
        System.out.println("timestamp to instant = " + Instant.ofEpochSecond(timestamp));

        Date res = DateUtil.unix_time_to_date(timestamp);
        System.out.println("res = " + res);
        System.out.println("=====================================================");
    }

    @Test
    public void test200() {
//        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Shanghai"));
//        deserialize();
//        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
//        deserialize();
        TimeZone.setDefault(TimeZone.getTimeZone("America/New_York"));
        deserialize();
    }

    private void deserialize() {
        System.out.println("Current TZ = " + ZoneId.systemDefault());
        String data = "{\"startTime\":\"2022-07-28T00:00:00.000+0000\",\"endTime\":\"2022-07-28T00:00:00.000+0000\"}";
        EmailModel model = JSONObject.parseObject(data, EmailModel.class);
        System.out.println(model);
        System.out.println("=====================================================");
    }

    @Test
    public void test210() {
        System.out.println(Locale.getDefault());
        Locale.setDefault(new Locale("en_US"));
        System.out.println(Locale.getDefault());
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Shanghai"));
        deserialize2();
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
        deserialize2();
        TimeZone.setDefault(TimeZone.getTimeZone("America/New_York"));
        deserialize2();
    }

    private void deserialize2() {
        System.out.println("Current TZ = " + ZoneId.systemDefault());
//        String msg = "{\"data\":{\"startTime\":\"2022-07-28T00:00:00.000+0000\",\"endTime\":\"2022-07-28T00:00:00.000+0000\"}}";
        String msg = "{\"data\":{\"startTime\":\"2022-07-30T00:00:00.000+0000\",\"endTime\":\"2022-07-30T00:00:00.000+0000\"}}";
        JSONObject jsonObj = JSONObject.parseObject(msg);
        JSONObject dataObj = jsonObj.getJSONObject("data");
        System.out.println("dataObj=" + dataObj);
        System.out.println("dataObj.toJSONString()=" + dataObj.toJSONString());
        EmailModel model = JSONObject.parseObject(dataObj.toJSONString(), EmailModel.class);
        System.out.println(model);
        System.out.println("=====================================================");
    }

    @Test
    public void test300() throws ValidationException, IOException {
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Shanghai"));
        generateIcs();
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
        generateIcs();
        TimeZone.setDefault(TimeZone.getTimeZone("America/New_York"));
        generateIcs();
    }

    private void generateIcs() throws IOException, ValidationException {
        System.out.println("Current TZ = " + ZoneId.systemDefault());
        String data = "{\"startTime\":\"2022-07-28T00:00:00.000+0000\",\"endTime\":\"2022-07-28T01:00:00.000+0000\"}";
        EmailModel model = JSONObject.parseObject(data, EmailModel.class);
        Date startTime = model.getStartTime();
        Date endTime = model.getEndTime();

        System.out.println("startTime = " + startTime);
        System.out.println("endTime = " + endTime);

        TimeZoneRegistry registry = TimeZoneRegistryFactory.getInstance()
                .createRegistry();
        net.fortuna.ical4j.model.TimeZone timeZone = registry.getTimeZone("America/New_York");
        VTimeZone tz = timeZone.getVTimeZone();

        net.fortuna.ical4j.model.DateTime start = new net.fortuna.ical4j.model.DateTime(startTime);
        System.out.println("start = " + start);
        start.setUtc(true);
        System.out.println("start = " + start);
        net.fortuna.ical4j.model.DateTime end = new DateTime(endTime);
        end.setUtc(true);
        VEvent meeting = new VEvent(start, end, "foo");
        System.out.println("before:");
        System.out.println(meeting);
        meeting.getProperties().add(tz.getTimeZoneId());
        System.out.println("end:");
        System.out.println(meeting);

        net.fortuna.ical4j.model.Calendar icsCalendar = new net.fortuna.ical4j.model.Calendar();
        icsCalendar.getProperties().add(
                new ProdId("Microsoft Exchange Server 2010"));
        icsCalendar.getProperties().add(Version.VERSION_2_0);
        icsCalendar.getProperties().add(Method.REQUEST);
        icsCalendar.getComponents().add(meeting);

//        FileOutputStream fout = new FileOutputStream(ZoneId.systemDefault() + ".ics");
        FileOutputStream fout = new FileOutputStream(ZoneId.systemDefault().toString().replace("/", "-") + ".ics");

        CalendarOutputter outPutter = new CalendarOutputter(false);
        outPutter.output(icsCalendar, fout);
        System.out.println("=====================================================");
    }

}
