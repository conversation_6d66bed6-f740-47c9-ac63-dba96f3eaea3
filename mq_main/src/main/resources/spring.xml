<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop" xmlns:motan="http://api.weibo.com/schema/motan"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd http://api.weibo.com/schema/motan http://api.weibo.com/schema/motan.xsd">

    <context:annotation-config/>
    <context:component-scan base-package="com.cpvsn"/>

    <motan:annotation package="com.cpvsn" />

    <aop:aspectj-autoproxy />

    <bean id="MailDataSource" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
        <property name="driverClassName" value="${ks_db.driverClassName}"/>
        <property name="url" value="${ks_db.url}"/>
        <property name="username" value="${ks_db.username}"/>
        <property name="password" value="${ks_db.password}"/>
        <property name="validationQuery" value="SELECT 1"/>
        <property name="testOnBorrow" value="true"/>
    </bean>

    <bean id="UdbDataSource" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
        <property name="driverClassName" value="${us_db.driverClassName}"/>
        <property name="url" value="${us_db.url}"/>
        <property name="username" value="${us_db.username}"/>
        <property name="password" value="${us_db.password}"/>
        <property name="validationQuery" value="SELECT 1"/>
        <property name="testOnBorrow" value="true"/>
    </bean>

    <bean id="MailSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="MailDataSource" />
        <property name="configLocation" value="classpath:mybatis/mail_mapper_config.xml" />
    </bean>

    <bean id="UdbSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="UdbDataSource" />
        <property name="configLocation" value="classpath:mybatis/udb_mapper_config.xml" />
    </bean>

    <context:property-placeholder location="classpath:properties/jdbc.properties"/>

</beans>