#rootLogger
log4j.rootLogger=INFO,Daily,ERROR,Console
log4j.appender.Console=org.apache.log4j.ConsoleAppender
log4j.appender.Console.layout=org.apache.log4j.PatternLayout
log4j.appender.Console.layout.ConversionPattern=%d [%t] %-5p %c(%L) - %m%n

log4j.appender.Daily=org.apache.log4j.DailyRollingFileAppender
log4j.appender.Daily.File=~/data/logs/mq_handler/log.log
log4j.appender.Daily.DatePattern='.'yyyy-MM-dd
log4j.appender.Daily.Append=true
log4j.appender.Daily.Threshold=INFO
log4j.appender.Daily.layout=org.apache.log4j.PatternLayout
log4j.appender.Daily.layout.ConversionPattern=%-d{HH:mm:ss} [%t] [%p] -%m%n

log4j.appender.ERROR=org.apache.log4j.DailyRollingFileAppender
log4j.appender.ERROR.File=~/data/logs/mq_handler/log_error.log
log4j.appender.ERROR.DatePattern='.'yyyy-MM-dd
log4j.appender.ERROR.Append=true
log4j.appender.ERROR.Threshold =ERROR
log4j.appender.ERROR.layout=org.apache.log4j.PatternLayout
log4j.appender.ERROR.layout.ConversionPattern=%-d{HH:mm:ss} [%t] [%p] -%m%n


# [rpc]
log4j.logger.serviceStatsLog=info, rpc_info
log4j.additivity.serviceStatsLog=false
log4j.logger.info=info, rpc_info
log4j.additivity.info=false
log4j.appender.rpc_info=org.apache.log4j.DailyRollingFileAppender
log4j.appender.rpc_info.file=~/data/logs/mq_handler/rpc_info.log
log4j.appender.rpc_info.DatePattern='.'yyyyMMdd
log4j.appender.rpc_info.layout=org.apache.log4j.PatternLayout
log4j.appender.rpc_info.layout.ConversionPattern=%-d{HH:mm:ss} %m%n
