<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="MqRetryMessage">

    <insert id="insert" parameterType="MqRetryMessage" >
        insert into mq_retry_message(`message_id`, `retry_count`, create_time, data)
        values(#{messageId}, #{retryCount}, #{createTime}, #{data})
        ON DUPLICATE KEY UPDATE
        retry_count = retry_count + 1,
        data = #{data}
    </insert>

    <select id="select_by_message_id" parameterType="Map" resultType="MqRetryMessage">
        SELECT * FROM mq_retry_message WHERE  message_id = #{message_id}
    </select>

</mapper>