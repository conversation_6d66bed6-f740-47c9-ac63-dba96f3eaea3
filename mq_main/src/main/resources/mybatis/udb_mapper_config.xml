<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <setting name="cacheEnabled" value="true" />
        <setting name="lazyLoadingEnabled" value="false" />
        <setting name="useGeneratedKeys" value="true" />
        <setting name="logPrefix" value="database.pay-" />
        <setting name="mapUnderscoreToCamelCase" value="true" />
        <setting name="logImpl" value="LOG4J"/>
    </settings>

    <typeAliases>
        <typeAlias alias="MqRetryMessage" type="com.cpvsn.mq.pojo.EmailRecord" />
    </typeAliases>

    <mappers>
        <mapper resource="mybatis/mapper_udb_email_record.xml" />
    </mappers>

</configuration>