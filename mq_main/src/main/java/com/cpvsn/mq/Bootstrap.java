package com.cpvsn.mq;

import com.cpvsn.core.util.LogUtil;
import org.slf4j.Logger;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;
import org.springframework.stereotype.Component;

/**
 * 启动类
 * Created by cosx on 2015/10/18.
 */
@Component
public class Bootstrap extends BaseBootstrap {

    private final static Logger log = LogUtil.getLog(Bootstrap.class);

    public static void main(String[] args) {
        ApplicationContext context = new ClassPathXmlApplicationContext("classpath:spring.xml");

        Bootstrap bootstrap = context.getBean(Bootstrap.class);

        try {
            bootstrap.start();
            log.info("service start");
        } catch (Exception e) {
            log.error("[Bootstrap]", e);
        }
    }

}
