package com.cpvsn.mq.util;

import com.alibaba.fastjson.JSONObject;
import com.cpvsn.mq.common.config.MqConfig;
import com.cpvsn.mq.constant.QueueConst;
import com.rabbitmq.client.ConnectionFactory;
import org.aeonbits.owner.ConfigFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.SimpleMessageConverter;

/**
 * Created by pingli on 2018/5/28.
 */
public class MqUtil {
    private static RabbitTemplate template;

    static {
        MqConfig mqConfig = ConfigFactory.create(MqConfig.class);

        ConnectionFactory cf = new ConnectionFactory();
        cf.setHost(mqConfig.hostname());
        cf.setPort(mqConfig.port());
        cf.setUsername(mqConfig.username());
        cf.setPassword(mqConfig.password());

        CachingConnectionFactory factory = new CachingConnectionFactory(cf);

        template = new RabbitTemplate(factory);
        SimpleMessageConverter messageConverter = new SimpleMessageConverter();
        /** 设置创建message时自动创建message id **/
        messageConverter.setCreateMessageIds(true);
//        ((SimpleMessageConverter)template.getMessageConverter()).setCreateMessageIds(true);
        template.setMessageConverter(messageConverter);
    }

    public static void sendMailMq(Object msg) {
        sendMailMsg(msg, QueueConst.QUEUE_MAIL_RATE);
    }

    public static void sendToMailMq(Object msg) {
        sendMailMsg(msg, QueueConst.QUEUE_MAIL);
    }

    public static void sendToMailOutreachMq(Object msg) {
        sendMailMsg(msg, QueueConst.QUEUE_MAIL_OUTREACH);
    }

    public static void sendMailMsg(Object msg, String queue) {
        String routeKey = QueueConst.build_route_key(queue);
        template.convertAndSend(routeKey + ".direct", routeKey, msg);
    }

}
