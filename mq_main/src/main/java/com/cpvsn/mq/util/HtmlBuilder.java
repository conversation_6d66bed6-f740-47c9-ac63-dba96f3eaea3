package com.cpvsn.mq.util;

/**
 * Created by cosx on 2015/11/13.
 */
public class HtmlBuilder {

    public static String array2HTML(Object[][] array){
        StringBuilder html = new StringBuilder(
                "<table border='2'>");
        for(Object elem:array[0]){
            html.append("<th>").append(elem.toString()).append("</th>");
        }
        for(int i = 1; i < array.length; i++){
            Object[] row = array[i];
            html.append("<tr align='left'>");
            for(Object elem:row){
                html.append("<td>").append(String.valueOf(elem)).append("</td>");
            }
            html.append("</tr>");
        }
        html.append("</table>");
        return html.toString();
    }

}
