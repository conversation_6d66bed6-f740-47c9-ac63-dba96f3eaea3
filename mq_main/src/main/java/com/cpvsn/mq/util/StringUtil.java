package com.cpvsn.mq.util;

import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.TimeZone;

/**
 * Created by cosx on 2016/3/4.
 */
public class StringUtil {

    public static boolean isCollectionsWithEmptyStr(Collection<String> strList) {
        for (String s : strList) {
            if (StringUtils.isEmpty(s)) {
                return true;
            }
        }

        return false;
    }

    /**
     *  移除空白字符
     */
    public static String removeTrim(String pwd) {
        if (pwd == null || "".equals(pwd.trim())) {
            return "";
        }
        return pwd.replaceAll("\\s+", "");//替换所有空白字符
    }

    public static String concat(String str, String... args) {
        StringBuilder sb = new StringBuilder();
        if (str != null) {
            sb.append(str);
        }

        for (String arg : args) {
            if (arg == null) {
                continue;
            }
            sb.append(arg);
        }

        return sb.toString();
    }

    public static void main(String[] args) {
        List<String> s = new ArrayList<String>();
        s.add("");

        System.out.println(StringUtil.isCollectionsWithEmptyStr(s));
        System.out.println(TimeZone.getTimeZone("UTC"));
        System.out.println(concat(null, "a", "b"));
    }
}
