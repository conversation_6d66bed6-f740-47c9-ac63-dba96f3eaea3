package com.cpvsn.mq.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * Created by pingli on 2021/5/16.
 */
public class DateUtil {

	public static final DateTimeFormatter SDF_HM = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
	public static final DateTimeFormatter SDF_SHORT_HM = DateTimeFormatter.ofPattern("yyyyMMddHHmm");

	public static final SimpleDateFormat SDF_FULL_TIMEZONE = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX");

	public static final String US_TIMEZONE = "America/New_York";

	/**
	 * 获取指定时区时间为与默认时区的date字符串时间的时间戳(例如date 时间为2021-10-18 12:00北京时间，zone设定为UTC，则返回的时间 为2021-10-18 12:00 UTC时间的时间戳
	 * @param date
	 * @param zone_id
	 * @return
	 */
	public static long date_to_unix_time(Date date, ZoneId zone_id) {
		LocalDateTime local_date_time = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

		ZonedDateTime zone_time = ZonedDateTime.of(local_date_time.getYear(), local_date_time.getMonthValue(),
				local_date_time.getDayOfMonth(), local_date_time.getHour(), local_date_time.getMinute(),
				local_date_time.getSecond(), local_date_time.getNano(), zone_id);

		return zone_time.toEpochSecond();
	}

	/**
	 * 返回指定时间戳对应的默认时区的date
	 * @param unix_time
	 * @return
	 */
	public static Date unix_time_to_date(long unix_time) {
		return Date.from(Instant.ofEpochSecond(unix_time));
	}


	public static String date_str(LocalDateTime localDateTime, DateTimeFormatter formatter) {
		return formatter.format(localDateTime);
	}

	public static String date_str(Date date, SimpleDateFormat formatter) {
		return formatter.format(date);
	}

	public static Date str_to_date(String date_str, DateTimeFormatter formatter) throws ParseException {
		return Date.from(LocalDateTime.parse(date_str, formatter).atZone(ZoneId.systemDefault()).toInstant());
	}
}
