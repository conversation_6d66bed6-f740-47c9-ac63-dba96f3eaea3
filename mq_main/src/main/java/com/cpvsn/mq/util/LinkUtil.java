package com.cpvsn.mq.util;

import com.cpvsn.mq.config.MainConfig;
import org.aeonbits.owner.ConfigFactory;

/**
 * Created by cosx on 2015/11/12.
 */
public class LinkUtil {

    private final static MainConfig mainConfig = ConfigFactory.create(MainConfig.class);

    public static String returnMailLink(String link) {
        return String.format("<a href=\"%s\">%s</a>", link, link);
    }

    public static String returnMailLink(String link, Object val) {
        return String.format("<a href=\"%s\">%s</a>", link, String.valueOf(val));
    }

    public static String returnProjectIdLink(int projectId) {
        return returnProjectIdLink(projectId, String.valueOf(projectId));
    }

    public static String returnProjectIdLink(int projectId, String value) {
        return returnMailLink("http://" + mainConfig.ndb_host() +
                "/proj_consultation/index/tasklist?id=" + projectId, value);
    }

    public static String returnConsultantLink(int consultantId, String consultantName) {
        return returnMailLink("http://" + mainConfig.ndb_host() +
                "/consultant/index/view?id=" + consultantId, consultantName);
    }

}
