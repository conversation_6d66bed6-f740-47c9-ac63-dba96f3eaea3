package com.cpvsn.mq.listener;

import com.alibaba.fastjson.JSONObject;
import com.cpvsn.core.util.LogUtil;
import com.cpvsn.mq.common.SpringContext;
import com.cpvsn.mq.common.exception.MqMailException;
import com.cpvsn.mq.common.model.email.EmailAttModel;
import com.cpvsn.mq.common.model.email.EmailModel;
import com.cpvsn.mq.dao.MqRetryMessageDao;
import com.cpvsn.mq.pojo.MqRetryMessage;
import com.rabbitmq.client.Channel;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.ChannelAwareMessageListener;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;

/**
 * 需要手动确认的RabbitMq接收收消息基类
 * Created by pingli on 2016/3/16.
 */
public abstract class AcknowledgeMqListener implements ChannelAwareMessageListener {

    private Logger log = LogUtil.getLog(AcknowledgeMqListener.class);
    private MqRetryMessageDao mqRetryMessageDao;

    /**
     * RabbitMq采用手动确认消息处理完成的模式是接收和处理消息的函数
     */
    @Override
    public void onMessage(Message message, Channel channel) {
        mqRetryMessageDao = SpringContext.getApplicationContext().getBean(MqRetryMessageDao.class);
        String msg = null;
        boolean ack = true;

        try {
            MqRetryMessage mqRetryMessage = mqRetryMessageDao.getById(message.getMessageProperties().getMessageId());
            /** 如果一条消息处理次数超过五次则放弃 **/
            if (mqRetryMessage != null && mqRetryMessage.getRetryCount() >= 5) {
                log.error("[AcknowledgeMqListener] message retry count more than three times, abandon it. message_id:{}", message.getMessageProperties().getMessageId());
            } else {
                msg = new String(message.getBody(), "UTF-8");
                log.info("[AcknowledgeMqListener] handler message. message_id:{}", message.getMessageProperties().getMessageId());
                handleMessage(msg);
            }
        } catch (MqMailException e) {
            /** 出现指定异常则不需要重新处理消息 **/
            log.error("[AcknowledgeMqListener] MqMailException", e);
        } catch (Exception e) {
            try {
                log.error("[AcknowledgeMqListener] Exception", e);
                /** 如果message id 为 null 则直接舍弃 **/
                if (!StringUtils.isEmpty(message.getMessageProperties().getMessageId())) {
                    insertMqRetryMessage(message, msg);

                    Thread.sleep(3000);
                    log.info("[AcknowledgeMqListener] nack message. message_id:{}", message.getMessageProperties().getMessageId());
                    /** 连接邮件服务器超时异常，返回unack **/
                    channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);

                    ack = false;
                }
            } catch (Exception e1) {
                log.error("[AcknowledgeMqListener] nack error", e1);
            }
        } finally {
            try {
                if (ack) {
                    log.info("[AcknowledgeMqListener] ack message. message_id:{}", message.getMessageProperties().getMessageId());
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                }
            } catch (Exception e) {
                log.error("[AcknowledgeMqListener] ack error ", e);
            }
        }
    }

    /**
     * 处理消息
     */
    protected abstract void handleMessage(String message) throws Exception;

    /**
     * 保存重试的消息
     */
    private void insertMqRetryMessage(Message message, String content) {
        String routeKey = message.getMessageProperties().getReceivedRoutingKey();
        switch (routeKey) {
            case "mail":
            case "delay_mail":
            case "to_mail":
                // 不保存 附件内容，否则附件内容太大超出数据库text的范围
                JSONObject jsonObj = JSONObject.parseObject(content);
                JSONObject dataObj = jsonObj.getJSONObject("data");
                EmailModel emailModel = JSONObject.parseObject(dataObj.toJSONString(), EmailModel.class);
                if (emailModel != null && emailModel.getAttList() != null && emailModel.getAttList().size() > 0) {
                    emailModel.setAttList(new ArrayList<EmailAttModel>());
                    jsonObj.put("data", emailModel);
                    content = jsonObj.toJSONString();
                }
                break;
            default:
                break;
        }

        MqRetryMessage mqRetryMessage = new MqRetryMessage();
        mqRetryMessage.setMessageId(message.getMessageProperties().getMessageId());
        mqRetryMessage.setCreateTime(new Date());
        mqRetryMessage.setRetryCount(1);
        mqRetryMessage.setData(content);

        mqRetryMessageDao.insert(mqRetryMessage);
    }
}
