package com.cpvsn.mq.service;

import com.cpvsn.mq.common.model.email.BatchEmailModel;
import com.cpvsn.mq.common.model.email.EmailModel;

import java.util.List;

/**
 * Created by cosx on 2015/11/5.
 */
public interface MailService {

    boolean sendMail(EmailModel emailModel) throws Exception;

    /**
     * 批量发送邮件
     * @param batchEmailModel
     * @return
     * @throws Exception
     */
    boolean sendBatchMail(BatchEmailModel batchEmailModel) throws Exception;

    boolean sendCalendar(EmailModel emailModel) throws Exception;

    boolean cancelCalendar(EmailModel emailModel) throws Exception;

    EmailModel filterEmailModel(EmailModel em);
}
