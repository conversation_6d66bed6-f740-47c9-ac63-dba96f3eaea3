package com.cpvsn.mq.service.impl;

import com.cpvsn.core.util.LogUtil;
import com.cpvsn.mq.common.model.email.BatchEmailModel;
import com.cpvsn.mq.common.model.email.EmailAttModel;
import com.cpvsn.mq.common.model.email.EmailModel;
import com.cpvsn.mq.config.MainConfig;
import com.cpvsn.mq.constant.MailConst;
import com.cpvsn.mq.loader.ConfigLoader;
import com.cpvsn.mq.mail.ExchangeSender;
import com.cpvsn.mq.mail.GoogleApiSender;
import com.cpvsn.mq.mail.MicrosoftGraphApiSender;
import com.cpvsn.mq.service.MailService;
import com.cpvsn.mq.util.DateUtil;
import com.mashape.unirest.http.exceptions.UnirestException;
import org.aeonbits.owner.ConfigFactory;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Email Service impl
 * Created by cosx on 2015/10/19.
 */
@Primary
@Component("mq_mail_service")
public class MailServiceImpl implements MailService {

    private Logger log = LogUtil.getLog(MailService.class);

    public final static String BATCH_EXCHANGE = "batch_exchange";
    public final static String BATCH_SEND_CLOUD = "batch_send_cloud";

    public final static String SEND_CALENDAR = "calendar";
    public final static String CANCEL_CALENDAR = "cancel_calendar";

    public final static String BATCH_SEND_CLOUD_CALENDAR = "batch_send_cloud_calendar";
    public final static String MSG_TYP_MAIL = "Mail";
    public final static String BATCH_EMAIL = "batch_email";

    private final static Set<String> ntpList = new HashSet<>();

    private final static MainConfig mainConfig = ConfigFactory.create(MainConfig.class);

    static {
        Collections.addAll(ntpList, mainConfig.ntp_mail_group().split(","));
    }

    @Autowired
    private SendgridMailService sendgridMailService;
    @Autowired
    private PostMarkService postMarkService;
    @Autowired
    private ResendService resendService;
    @Autowired
    private MailgunService mailgunService;
    @Autowired
    private MailjetService mailjetService;

    @Override
    public boolean sendMail(EmailModel emailModel) throws Exception {
        filterEmailModel(emailModel);

        if (StringUtils.isEmpty(emailModel.getPrefer_vendor())) {
            emailModel.setPrefer_vendor(ConfigLoader.getMainConfig().mail_send_default_vendor());
        }
        if (MailConst.DB_SENDER_MAIL.equals(emailModel.getFrom())) {
            emailModel.setFrom(ConfigLoader.getMainConfig().proxy_send_email_default());
        }

        EmailModel.Vendor vendor = getVendor(emailModel.getPrefer_vendor());
        if (vendor == null) return false;

        switch (vendor) {
            case SEND_GRID:
                return sendgridMailService.sendMail(emailModel);
            case POSTMARK:
                return postMarkService.sendMail(emailModel);
            case EXCHANGE:
                MicrosoftGraphApiSender.sendEmailWithAttachments(emailModel);
                break;
            case RESEND:
                resendService.sendEmail(emailModel);
                break;
            case MAILGUN:
                return mailgunService.sendMail(emailModel);
            case MAILJET:
                return mailjetService.sendMail(emailModel);
            case SEND_CLOUD:
            case UNKNOWN:
            default:
                log.error("Unsupported vendor: " + emailModel.getPrefer_vendor());
                return false;
        }

        return true;
    }


    @Override
    public boolean sendBatchMail(BatchEmailModel batchEmailModel) throws Exception {
        if (CollectionUtils.isEmpty(batchEmailModel.getEmail_model_list())) {
            return true;
        }

        EmailModel.Vendor vendor = getVendor(batchEmailModel.getPrefer_vendor());
        if (EmailModel.Vendor.RESEND == vendor) {
            for (EmailModel emailModel : batchEmailModel.getEmail_model_list()) {
                filterEmailModel(emailModel);
            }

            resendService.sendBatchEmail(batchEmailModel.getEmail_model_list());
            return true;
        }


        for (EmailModel emailModel : batchEmailModel.getEmail_model_list()) {
            emailModel.setPrefer_vendor(batchEmailModel.getPrefer_vendor());
            sendMail(emailModel);
        }

        return true;
    }

    @Override
    public boolean sendCalendar(EmailModel emailModel) throws Exception {
        filterEmailModel(emailModel);

        if (StringUtils.endsWith(emailModel.getFrom(), MailConst.CAPVISION_PRO_GMAIL_SUFFIX)) {
            GoogleApiSender.sendCalendar(emailModel);
        } else {
            MicrosoftGraphApiSender.sendCalendar(emailModel);
        }

        return true;

//        EmailModel copyEmail = SerializationUtils.clone(emailModel);
//        /** 使用dbsender把日历发给发件人自己，否则发件人无法收到日历 ,先把日历发送给发件人，然后再发给客户，这样可以保证 发生邮件服务器连接超时异常时重新处理消息，重新发送日历时不会给客户发送多封 ，
//         * 要么是在给发件人都出现邮件服务器连接超时异常，此时还没有给客户发日历；要么给发件人发日历成功，但是客户出现邮件服务器连接超时异常，这两种情况下重发日历都不会导致客户重复收到日历，
//         * 如果是先给客户发送，后给发件人发送则可能会出现,客户收到重复日历
//         * **/
//        emailModel.getTo().clear();
//        emailModel.getCc().clear();
//        emailModel.getBcc().clear();
//        emailModel.getTo().add(emailModel.getFrom());
//
//        ExchangeSender.sendEventEmail(
//                emailModel.getCalendarUid(),
//                emailModel.getSequence(),
//                MailConst.SYSTEM_CALENDAR_EMAIL,
//                MailConst.SYSTEM_CALENDAR_EMAIL_NAME,
//                emailModel.getTo(),
//                emailModel.getCc(),
//                emailModel.getStartTime(),
//                emailModel.getEndTime(),
//                emailModel.getLocation(),
//                emailModel.getSubject(),
//                emailModel.getContent()
//        );
//
//        ExchangeSender.sendEventEmail(
//                copyEmail.getCalendarUid(),
//                copyEmail.getSequence(),
//                copyEmail.getFrom(),
//                copyEmail.getFromName(),
//                copyEmail.getTo(),
//                copyEmail.getCc(),
//                copyEmail.getStartTime(),
//                copyEmail.getEndTime(),
//                copyEmail.getLocation(),
//                copyEmail.getSubject(),
//                copyEmail.getContent()
//        );
//
//        return true;
    }

    @Override
    public boolean cancelCalendar(EmailModel emailModel) throws Exception {
        filterEmailModel(emailModel);

        if (StringUtils.endsWith(emailModel.getFrom(), MailConst.CAPVISION_PRO_GMAIL_SUFFIX)) {
            GoogleApiSender.cancelCalendar(emailModel);
        } else {
            MicrosoftGraphApiSender.cancelCalendar(emailModel);
        }
        return true;

//        EmailModel copyEmail = SerializationUtils.clone(emailModel);
//
//        emailModel.getTo().clear();
//        emailModel.getCc().clear();
//        emailModel.getBcc().clear();
//        emailModel.getTo().add(emailModel.getFrom());
//        ExchangeSender.cancelEventEmail(
//                emailModel.getCalendarUid(),
//                emailModel.getSequence(),
//                MailConst.SYSTEM_CALENDAR_EMAIL,
//                MailConst.SYSTEM_CALENDAR_EMAIL_NAME,
//                emailModel.getTo(),
//                emailModel.getCc(),
//                emailModel.getStartTime(),
//                emailModel.getEndTime(),
//                emailModel.getLocation(),
//                emailModel.getSubject(),
//                emailModel.getContent()
//        );
//
//        ExchangeSender.cancelEventEmail(
//                copyEmail.getCalendarUid(),
//                copyEmail.getSequence(),
//                copyEmail.getFrom(),
//                copyEmail.getFromName(),
//                copyEmail.getTo(),
//                copyEmail.getCc(),
//                copyEmail.getStartTime(),
//                copyEmail.getEndTime(),
//                copyEmail.getLocation(),
//                copyEmail.getSubject(),
//                copyEmail.getContent()
//        );
//
//        return true;
    }

    @Override
    public EmailModel filterEmailModel(EmailModel em) {
        if (StringUtils.isEmpty(em.getType())) {
            em.setType(MailConst.TYPE_EXCHANGE);
        }

        if (em.getCc() == null) {
            em.setCc(new ArrayList<String>());
        }

        if (em.getBcc() == null) {
            em.setBcc(new ArrayList<String>());
        }

        if (em.getAttList() == null) {
            em.setAttList(new ArrayList<>());
        }

        if (em.getHeads() == null) {
            em.setHeads(new HashMap<>());
        }

        if (em.getReply_to_list() == null) {
            em.setReply_to_list(new ArrayList<>());
        }

        em.setTo(em.getTo().stream().map(String::toLowerCase).distinct().collect(Collectors.toList()));
        em.setCc(em.getCc().stream().map(String::toLowerCase).distinct().collect(Collectors.toList()));
        em.setBcc(em.getBcc().stream().map(String::toLowerCase).distinct().collect(Collectors.toList()));

        List<String> mailList = new ArrayList<>();

        em.setSender_type(MailConst.SENDER_TYPE_INNER_ALERT);
        mailList.addAll(em.getTo());
        mailList.addAll(em.getCc());
        mailList.addAll(em.getBcc());
        for (String mail : mailList) {
            if (!mail.trim().endsWith("capvision.com")) {
                em.setSender_type(MailConst.SENDER_TYPE_COMMON);
                break;
            }
        }

        if (em.getStartTime() != null) {
            log.info("actual start time: {} ", DateUtil.date_str(em.getStartTime(), DateUtil.SDF_FULL_TIMEZONE));
        }

        em.setAttList(em.getAttList().stream().map(att -> {
            EmailAttModel attModel = new EmailAttModel();
            if (att.getAtt() != null) {
                return att;
            }

            if (!StringUtils.isEmpty(att.getFilePath())) {
                try {
                    attModel = ExchangeSender.build_att_by_file_path(att.getFilePath(), att.getAttName());
                } catch (UnirestException unirestException) {
                    unirestException.printStackTrace();
                } catch (IOException ioException) {
                    ioException.printStackTrace();
                }
            }

            return attModel;
        }).filter(e -> e.getAtt() != null && e.getAtt().length > 0).collect(Collectors.toList()));


//        /** 美国rm-server项目传过来时间都是utc的时间，但是使用fastjson转换后，时间还是一样，但是时区变成了默认时区，所以需要先转换成utc，在把对应的utc时间转换成对应的默认时区的时间 **/
//        if (em.getStartTime() != null) {
//            em.setStartTime(DateUtil.unix_time_to_date(
//                    DateUtil.date_to_unix_time(em.getStartTime(), TimeZone.getTimeZone("UTC").toZoneId())));
//        }
//        if (em.getEndTime() != null) {
//            em.setEndTime(DateUtil.unix_time_to_date(
//                    DateUtil.date_to_unix_time(em.getEndTime(), TimeZone.getTimeZone("UTC").toZoneId())));
//        }

        return em;
    }

    private EmailModel.Vendor getVendor(String preferVendor) {
        EmailModel.Vendor vendor = null;
        try {
            vendor = EmailModel.Vendor.valueOf(preferVendor);
        } catch (IllegalArgumentException e) {
            log.error("Unsupported vendor : " + preferVendor);
            return EmailModel.Vendor.UNKNOWN;
        }
        return vendor;
    }

}
