package com.cpvsn.mq.service.impl;

import com.cpvsn.core.factory.CoreFactory;
import com.cpvsn.core.service.RedisService;
import com.cpvsn.core.util.LogUtil;
import com.cpvsn.mq.common.model.email.EmailAttModel;
import com.cpvsn.mq.common.model.email.EmailModel;
import com.cpvsn.mq.constant.MailConst;
import com.cpvsn.mq.dao.EmailRecordDao;
import com.cpvsn.mq.loader.ConfigLoader;
import com.cpvsn.mq.mail.ExchangeSender;
import com.cpvsn.mq.util.StringUtil;
import com.mashape.unirest.http.exceptions.UnirestException;
import com.resend.Resend;
import com.resend.services.batch.model.CreateBatchEmailsResponse;
import com.resend.services.emails.model.Attachment;
import com.resend.services.emails.model.CreateEmailOptions;
import com.resend.services.emails.model.CreateEmailResponse;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.Base64;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ResendService {

    private Logger log = LogUtil.getLog(this.getClass());
    public final static String RESEND_CUSTOM_ARG_PREFIX = "RESEND_CUSTOM_ARGS_";
    public final static String RESEND_CUSTOM_ARG_EMAIL_RECORD_ID = "RESEND_CUSTOM_ARGS_email_record_id";
    private final Resend resend = new Resend(ConfigLoader.getMainConfig().resend_api_key());
    private final RedisService redisService = CoreFactory.getRedis();

    @Autowired
    private EmailRecordDao emailRecordDao;

    public boolean sendEmail(EmailModel emailModel) throws Exception {
        log.info("Resend send email");

        getLock();
        CreateEmailResponse response = resend.emails().send(buildEmail(emailModel));

        updateVendorEmailId(emailModel, response.getId());

        return true;
    }

    public boolean sendBatchEmail(List<EmailModel> emailModelList) {
        log.info("Resend send batch email");

        // 批量发送的时候,抛出异常,因为批量发送不好确定哪些发送成功,还是都没有发送成功,所以不适合重新尝试发送,同时,这个消息里面邮件内容太大,也不适合存储到数据库中
        try {
            getLock();
            CreateBatchEmailsResponse response = resend.batch().send(emailModelList.stream().map(this::buildEmail).collect(Collectors.toList()));
            for (int i = 0; i < emailModelList.size(); i++) {
                updateVendorEmailId(emailModelList.get(i), response.getData().get(i).getId());
            }
        } catch (Exception e) {
            log.error("[resend send batch email] error: {}", e.getMessage());
        }

        return true;
    }

    private CreateEmailOptions buildEmail(EmailModel emailModel) {
        CreateEmailOptions.Builder builder = CreateEmailOptions.builder()
                .from(StringUtils.isEmpty(emailModel.getFromName()) ? emailModel.getFrom() : StringUtil.concat(emailModel.getFromName(), " <", emailModel.getFrom(), ">"))
                .to(emailModel.getTo())
                .cc(emailModel.getCc())
                .bcc(emailModel.getBcc())
                .replyTo(emailModel.getReply_to_list())
                .subject(emailModel.getSubject())
                .html(emailModel.getContent());

        if (!CollectionUtils.isEmpty(emailModel.getAttList())) {
            emailModel.getAttList().forEach(e -> {
                    builder.addAttachment(Attachment.builder().fileName(e.getAttName()).content(Base64.getEncoder().encodeToString(e.getAtt())).build());
            });

            //附加header信息,主要是退订的头部信息List-Unsubscribe
            emailModel.getHeads().forEach( (k, v) -> {
                if ("List-Unsubscribe".equals(k)) {
                    builder.addHeader("List-Unsubscribe-Post", "List-Unsubscribe=One-Click");
                }
                builder.addHeader(k, v);
            });

            if (org.apache.commons.lang.StringUtils.isNotEmpty(emailModel.getMessage_id())) {
                builder.addHeader("Message-ID", emailModel.getMessage_id());
            }
            if (org.apache.commons.lang.StringUtils.isNotEmpty(emailModel.getReply_message_id())) {
                builder.addHeader("In-Reply-To", emailModel.getReply_message_id());
            }
        }

        return builder.build();

    }

    private void getLock() throws InterruptedException {
        int count = 0;

        // resend 限制每秒最多两个api请求
        while (redisService.set_nx("resend_lock", "1") != 1 && count < 60) {
            Thread.sleep(1000);
            /** 添加一个count， 最多尝试50次设置，如果超过60则直接跳过，超过60次可能是因为上一次redis set nx成功，但是设置过期时间失败，导致key对应的值一直存在，无法set nx成功， 加一个count限制，可以防止这种情况一直循环下去 **/
            count++;
        }

        redisService.expire("resend_lock", 2);
    }

    private void updateVendorEmailId(EmailModel emailModel, String vendorEmailId) {
        int id = 0;
        try {
            id = Integer.parseInt(emailModel.getExtra_params().get(RESEND_CUSTOM_ARG_EMAIL_RECORD_ID).toString());
        } catch (Exception e) {
            log.error("[ResendService updateVendorEmailId] error: {}", e.getMessage());
            return;
        }

        emailRecordDao.update(id, EmailModel.Vendor.RESEND.name() + "-" + vendorEmailId);
    }
    }
