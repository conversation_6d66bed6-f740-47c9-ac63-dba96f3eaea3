package com.cpvsn.mq.service.impl;

import com.cpvsn.core.util.LogUtil;
import com.cpvsn.mq.common.model.email.EmailModel;
import com.cpvsn.mq.loader.ConfigLoader;
import com.mailgun.api.v3.MailgunMessagesApi;
import com.mailgun.client.MailgunClient;
import com.mailgun.model.message.Message;
import feign.form.FormData;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Service
public class MailgunService {
    private Logger log = LogUtil.getLog(this.getClass());

    private final MailgunMessagesApi mailgunMessagesApi = MailgunClient.config(ConfigLoader.getMainConfig().mailgun_api_key()).createApi(MailgunMessagesApi.class);
    private final String DOMAIN = "capvision.com";
    public final static String MAILGUN_CUSTOM_ARG_PREFIX = "MAILGUN_CUSTOM_ARGS_";

    public boolean sendMail(EmailModel emailModel) {
        log.info("Mailgun send email");

        Message.MessageBuilder builder = Message.builder()
                .from(emailModel.getFrom())
                .sender(emailModel.getFromName())
                .subject(emailModel.getSubject())
                .to(emailModel.getTo())
                .html(emailModel.getContent())
                .requireTls(true);
        // mailgun cc 和 bcc 不能传空列表,会报错
        if (!CollectionUtils.isEmpty(emailModel.getCc())) {
            builder.cc(emailModel.getCc());
        }
        if (!CollectionUtils.isEmpty(emailModel.getBcc())) {
            builder.bcc(emailModel.getBcc());
        }

        Map<String, String> headerMap = new HashMap<>();
        if (StringUtils.isNotEmpty(emailModel.getMessage_id())) {
            headerMap.put("Message-ID", emailModel.getMessage_id());
        }
        if (StringUtils.isNotEmpty(emailModel.getReply_message_id())) {
            headerMap.put("In-Reply-To", emailModel.getReply_message_id());
        }
        //附加header信息,主要是退订的头部信息List-Unsubscribe
        emailModel.getHeads().forEach((k, v) -> {
            if ("List-Unsubscribe".equals(k)) {
                headerMap.put("List-Unsubscribe-Post", "List-Unsubscribe=One-Click");
            }
            headerMap.put(k, v);
        });
        builder.headers(headerMap);
        if (!CollectionUtils.isEmpty(emailModel.getReply_to_list())) {
            emailModel.getReply_to_list().stream().findFirst().ifPresent(builder::replyTo);
        }


        emailModel.getAttList().forEach(e-> {
            builder.formData(new FormData(e.getAttType(), e.getAttName(), e.getAtt()));
        });

        if (emailModel.getExtra_params() != null) {
            Map<String, Object> mailgun_var_map = new HashMap<>();
            emailModel.getExtra_params().forEach((k, v) -> {
                if (!k.startsWith(MAILGUN_CUSTOM_ARG_PREFIX)) {
                    return;
                }

                mailgun_var_map.put(k.substring(MAILGUN_CUSTOM_ARG_PREFIX.length()), v.toString());
            });
            builder.myVar(mailgun_var_map);

            Optional.ofNullable(emailModel.getExtra_params().get("email_tag")).ifPresent(t -> builder.tag(t.toString()));
        }

        try {
            mailgunMessagesApi.sendMessage(DOMAIN, builder.build());
        } catch (Exception e) {
          log.error("Mailgun send email error", e);
          return false;
        }

        return true;
    }
}
