package com.cpvsn.mq.service.impl;

import com.cpvsn.mq.common.model.email.EmailModel;
import com.cpvsn.mq.loader.ConfigLoader;
import com.mailjet.client.ClientOptions;
import com.mailjet.client.MailjetClient;
import com.mailjet.client.MailjetRequest;
import com.mailjet.client.MailjetResponse;
import com.mailjet.client.errors.MailjetException;
import com.mailjet.client.resource.Emailv31;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class MailjetService {
    private final Logger log = LoggerFactory.getLogger(MailjetService.class);

    private final MailjetClient client = new MailjetClient(ClientOptions.builder()
            .apiKey(ConfigLoader.getMainConfig().mailjet_api_key())
            .apiSecretKey(ConfigLoader.getMainConfig().mailjet_api_secret())
            .build());

    public final static String MAILJET_CUSTOM_ARG_PREFIX = "MAILJET_CUSTOM_ARGS_";

    public boolean sendMail(EmailModel model)  {
        log.info("Mailjet send email");

        JSONObject mail = new JSONObject();
        mail.put(Emailv31.Message.FROM, buildReceipt(model.getFrom(), model.getFromName()));
        mail.put(Emailv31.Message.TO, buildReceipts(model.getTo()));
        mail.put(Emailv31.Message.CC, buildReceipts(model.getCc()));
        mail.put(Emailv31.Message.BCC, buildReceipts(model.getBcc()));
        mail.put(Emailv31.Message.SUBJECT, model.getSubject());
        mail.put(Emailv31.Message.HTMLPART, model.getContent());
        if (!CollectionUtils.isEmpty(model.getAttList())) {
            mail.put(Emailv31.Message.ATTACHMENTS, model.getAttList().stream().map((a -> {
                JSONObject attachment = new JSONObject();

                attachment.put("ContentType", a.getAttType());
                attachment.put("Filename", a.getAttName());
                attachment.put("Base64Content",  Base64.getEncoder().encodeToString(a.getAtt()));

                return attachment;
            })).collect(Collectors.toList()));
        }

        Map<String, String> headerMap = new HashMap<>();
        if (org.apache.commons.lang.StringUtils.isNotEmpty(model.getMessage_id())) {
//            headerMap.put("Message-ID", model.getMessage_id());
            mail.put(Emailv31.Message.CUSTOMID, StringUtils.left(model.getMessage_id(), 64));
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(model.getReply_message_id())) {
            headerMap.put("In-Reply-To", model.getReply_message_id());
        }
        //附加header信息,主要是退订的头部信息List-Unsubscribe
        model.getHeads().forEach((k, v) -> {
            if ("List-Unsubscribe".equals(k)) {
                headerMap.put("List-Unsubscribe-Post", "List-Unsubscribe=One-Click");
            }
            headerMap.put(k, v);
        });
        mail.put(Emailv31.Message.HEADERS, headerMap);

        if (!CollectionUtils.isEmpty(model.getReply_to_list())) {
            model.getReply_to_list().stream().findFirst().ifPresent(r-> mail.put(Emailv31.Message.REPLYTO, buildReceipt(r, null)));
        }

        if (model.getExtra_params() != null) {
            JSONObject paramsObj = new JSONObject();
            model.getExtra_params().forEach((k, v) -> {
                if (!k.startsWith(MAILJET_CUSTOM_ARG_PREFIX)) {
                    return;
                }

                paramsObj.put(k.substring(MAILJET_CUSTOM_ARG_PREFIX.length()), v.toString());
            });
            Optional.ofNullable(model.getExtra_params().get("email_tag")).ifPresent(t -> paramsObj.put("tag", t.toString()));

            mail.put(Emailv31.Message.EVENTPAYLOAD, paramsObj.toString());
        }

        MailjetRequest request= new MailjetRequest(Emailv31.resource)
                .property(Emailv31.MESSAGES, Collections.singletonList(mail));

        MailjetResponse response = null;
        try {
            response = client.post(request);
        } catch (MailjetException e) {
            log.error("Mailjet send mail error", e);
            return false;
        }

        if (response.getStatus() != 201 && response.getStatus() != 200) {
            log.error("Mailjet send mail error, status: {}, data: {}", response.getStatus(), response.getRawResponseContent());
            return false;
        }

        return true;
    }

    private JSONObject buildReceipt(String email, String name) {
        JSONObject receipt = new JSONObject();

        receipt.put("Email", email);
        if (StringUtils.isNotEmpty(name)) {
            receipt.put("Name", name);
        }

        return receipt;
    }

    private JSONArray buildReceipts(List<String> emails) {
        JSONArray receipts = new JSONArray();

        emails.forEach(email -> {
            receipts.put(buildReceipt(email, email));
        });

        return receipts;
    }


}


