package com.cpvsn.mq.service.impl;

import com.cpvsn.core.exception.BusinessException;
import com.cpvsn.core.util.LogUtil;
import com.cpvsn.mq.common.model.email.EmailAttModel;
import com.cpvsn.mq.common.model.email.EmailModel;
import com.cpvsn.mq.config.MainConfig;
import com.cpvsn.mq.constant.MailConst;
import com.cpvsn.mq.mail.ExchangeSender;
import com.cpvsn.mq.util.StringUtil;
import com.mashape.unirest.http.exceptions.UnirestException;
import com.sendgrid.Method;
import com.sendgrid.Request;
import com.sendgrid.Response;
import com.sendgrid.SendGrid;
import com.sendgrid.helpers.mail.Mail;
import com.sendgrid.helpers.mail.objects.Attachments;
import com.sendgrid.helpers.mail.objects.Content;
import com.sendgrid.helpers.mail.objects.Email;
import com.sendgrid.helpers.mail.objects.Personalization;
import org.aeonbits.owner.ConfigFactory;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.HashSet;
import java.util.Set;

@Service
public class SendgridMailService {

    private Logger log = LogUtil.getLog(this.getClass());

    public final static String SENDGRID_CUSTOM_ARG_PREFIX = "SG_CUSTOM_ARGS_";
    public final static String SENDGRID_IP_POOL_NAME = "ip_pool_name";
    private final static MainConfig mainConfig = ConfigFactory.create(MainConfig.class);
    private final SendGrid sendgrid = new SendGrid(mainConfig.sendgrid_api_key());

    public boolean sendMail(EmailModel emailModel) throws Exception {
        log.info("Sendgrid send email");
        Request request = new Request();
        request.setMethod(Method.POST);
        request.setEndpoint("mail/send");

        Email from = new Email(emailModel.getFrom(), emailModel.getFromName());
        Personalization personalization = new Personalization();
        Set<String> email_set = new HashSet<>();
        emailModel.getTo().forEach(e -> {
            addSendgridEmail(e, MailConst.EMAIL_TO, personalization, email_set);
        });
        emailModel.getCc().forEach(e -> {
            addSendgridEmail(e, MailConst.EMAIL_CC, personalization, email_set);
        });
        emailModel.getBcc().forEach(e -> {
            addSendgridEmail(e, MailConst.EMAIL_BCC, personalization, email_set);
        });

        Mail mail = new Mail();
        mail.addPersonalization(personalization);
        mail.setSubject(emailModel.getSubject());
        mail.addContent(new Content("text/html", emailModel.getContent()));
        mail.setFrom(from);
        //附加header信息,主要是退订的头部信息List-Unsubscribe
        emailModel.getHeads().forEach( (k, v) -> {
            if ("List-Unsubscribe".equals(k)) {
                mail.addHeader("List-Unsubscribe-Post", "List-Unsubscribe=One-Click");
            }
            mail.addHeader(k, v);
        });


        emailModel.getAttList().forEach(e -> {
            ByteArrayInputStream stream = new ByteArrayInputStream(e.getAtt());
            Attachments.Builder builder = new Attachments.Builder(e.getAttName(), stream);
            mail.addAttachments(builder.build());
        });

        if (emailModel.getFrom().endsWith("@capvision.com")) {
            mail.setIpPoolId("outreach");
        } else if (emailModel.getFrom().endsWith("@capvision-pro.com")) {
            mail.setIpPoolId("outreach_pro");
        }

        if (emailModel.getExtra_params() != null) {
            emailModel.getExtra_params().forEach((k, v) -> {
                if (k.startsWith(SENDGRID_CUSTOM_ARG_PREFIX)) {
                    String key = k.substring(SENDGRID_CUSTOM_ARG_PREFIX.length());
                    mail.addCustomArg(key, v.toString());
                }

                if (k.equals(SENDGRID_IP_POOL_NAME) && v != null){
                    mail.setIpPoolId(v.toString());
                }
            });
        }
        if (emailModel.getReply_to_list() != null
                && !emailModel.getReply_to_list().isEmpty()) {
            if(emailModel.getReply_to_list().size() > 1) {
                if (log.isWarnEnabled()) {
                    log.warn("SendGrid Java SDK does not support Multiple 'reply-to' addresses, only 1 address will take effect");
                }
            }
            emailModel.getReply_to_list().stream().findFirst().ifPresent(replyTo -> {
                mail.setReplyTo(new Email(replyTo));
            });
        }

        if (StringUtils.isNotEmpty(emailModel.getMessage_id())) {
            mail.addHeader("Message-ID", emailModel.getMessage_id());
        }
        if (StringUtils.isNotEmpty(emailModel.getReply_message_id())) {
            mail.addHeader("In-Reply-To", emailModel.getReply_message_id());
        }

        request.setBody(mail.build());
        Response response = sendgrid.api(request);

        if (response.getStatusCode() >= 200 && response.getStatusCode() < 300) {
            if (response.getStatusCode() != 200) {
                log.warn("statusCode=" + response.getStatusCode());
            }
            return true;
        } else {
            throw new BusinessException("send failed, statusCode=" + response.getStatusCode() + ", body=" + response.getBody());
        }
    }


    private void addSendgridEmail(String email, int type, Personalization personalization, Set<String> exist_email_set) {
        /** sendgrid 不允许to，cc和bcc中有重复的邮件地址，重复了会报错，所以过滤掉重复的邮件地址 **/
        if (exist_email_set.contains(email)) {
            return;
        }
        Email email_obj = new Email(email);
        switch (type) {
            case MailConst.EMAIL_TO:
                personalization.addTo(email_obj);
                break;
            case MailConst.EMAIL_CC:
                personalization.addCc(email_obj);
                break;
            case MailConst.EMAIL_BCC:
                personalization.addBcc(email_obj);
                break;
        }

        exist_email_set.add(email);
    }

}
