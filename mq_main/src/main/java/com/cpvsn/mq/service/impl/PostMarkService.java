package com.cpvsn.mq.service.impl;

import com.cpvsn.core.util.LogUtil;
import com.cpvsn.mq.common.model.email.EmailAttModel;
import com.cpvsn.mq.common.model.email.EmailModel;
import com.cpvsn.mq.loader.ConfigLoader;
import com.cpvsn.mq.mail.ExchangeSender;
import com.mashape.unirest.http.exceptions.UnirestException;
import com.postmarkapp.postmark.Postmark;
import com.postmarkapp.postmark.client.ApiClient;
import com.postmarkapp.postmark.client.Parameters;
import com.postmarkapp.postmark.client.data.model.message.Message;
import com.postmarkapp.postmark.client.data.model.message.MessageResponse;
import com.postmarkapp.postmark.client.data.model.suppressions.Suppression;
import com.postmarkapp.postmark.client.data.model.suppressions.SuppressionEntries;
import com.postmarkapp.postmark.client.data.model.suppressions.SuppressionStatuses;
import com.postmarkapp.postmark.client.data.model.suppressions.Suppressions;
import com.postmarkapp.postmark.client.exception.PostmarkException;
import org.apache.commons.lang.StringUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Service
public class PostMarkService {

    private Logger log = LogUtil.getLog(this.getClass());

    public final static String POSTMARK_CUSTOM_ARG_PREFIX = "POSTMARK_CUSTOM_ARGS_";
    private final static ApiClient POSTMARK_CLIENT =  Postmark.getApiClient(ConfigLoader.getMainConfig().postmark_api_token());
    private final static String POSTMARK_STREAM_ID = "broadcast";


    public boolean sendMail(EmailModel emailModel) throws PostmarkException, IOException {
        log.info("Postmark send email");

        Message message = new Message(emailModel.getFrom(), "", emailModel.getSubject(), emailModel.getContent());
        message.setTo(emailModel.getTo());
        message.setCc(emailModel.getCc());
        message.setBcc(emailModel.getBcc());
        message.setFrom(emailModel.getFromName(), emailModel.getFrom());
        // postmark 要求批量推销邮件必须使用broadcast message stream
        message.setMessageStream(POSTMARK_STREAM_ID);

        //附加header信息,主要是退订的头部信息List-Unsubscribe
        emailModel.getHeads().forEach( (k, v) -> {
            if ("List-Unsubscribe".equals(k)) {
                message.addHeader("List-Unsubscribe-Post", "List-Unsubscribe=One-Click");
            }
            message.addHeader(k, v);
        });

        emailModel.getAttList().forEach(e -> {
            message.addAttachment(e.getAttName(), e.getAtt(), e.getAttType());
        });

        if (emailModel.getReply_to_list() != null
                && !emailModel.getReply_to_list().isEmpty()) {
            if(emailModel.getReply_to_list().size() > 1) {
                if (log.isWarnEnabled()) {
                    log.warn("PostMark Java SDK does not support Multiple 'reply-to' addresses, only 1 address will take effect");
                }
            }
            emailModel.getReply_to_list().stream().findFirst().ifPresent(message::setReplyTo);
        }
        if (StringUtils.isNotEmpty(emailModel.getMessage_id())) {
            message.addHeader("Message-ID", emailModel.getMessage_id());
        }
        if (StringUtils.isNotEmpty(emailModel.getReply_message_id())) {
            message.addHeader("In-Reply-To", emailModel.getReply_message_id());
        }

        if (emailModel.getExtra_params() != null) {
            emailModel.getExtra_params().forEach((k, v) -> {
                if (!k.startsWith(POSTMARK_CUSTOM_ARG_PREFIX)) {
                    return;
                }

                message.addMetadata(k.substring(POSTMARK_CUSTOM_ARG_PREFIX.length()), v.toString());
            });

            Optional.ofNullable(emailModel.getExtra_params().get("email_tag")).ifPresent(t -> message.setTag(t.toString()));
        }


        // 删除掉在 suppression列表中的凯盛邮箱,防止公司用户收不到
        Suppressions suppressions = POSTMARK_CLIENT.getSuppressions(POSTMARK_STREAM_ID, Parameters.init().build("EmailAddress", "capvision.com"));
        if (suppressions != null && !CollectionUtils.isEmpty(suppressions.getSuppressions())) {
            Set<String> emailSet = new HashSet<>(emailModel.getTo());
            emailSet.addAll(emailModel.getCc());
            emailSet.addAll(emailModel.getBcc());
            emailSet.add(emailModel.getFrom());

            String[] suppressionEmailArray = suppressions.getSuppressions().stream().map(Suppression::getEmailAddress).filter(emailSet::contains).toArray(String[]::new);
            if (suppressionEmailArray.length > 0) {
                POSTMARK_CLIENT.deleteSuppressions(POSTMARK_STREAM_ID, new SuppressionEntries(suppressionEmailArray));

                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }



        MessageResponse response = POSTMARK_CLIENT.deliverMessage(message);
        if (0 !=response.getErrorCode()) {
            log.error( "PostMark send mail failed: " + JSONObject.valueToString(response));
            return false;
        }

        return true;
    }
}
