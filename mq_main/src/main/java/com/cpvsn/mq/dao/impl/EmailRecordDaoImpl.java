package com.cpvsn.mq.dao.impl;

import com.cpvsn.mq.dao.EmailRecordDao;
import com.cpvsn.mq.dao.UdbBaseDao;
import com.cpvsn.mq.pojo.EmailRecord;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class EmailRecordDaoImpl extends UdbBaseDao<EmailRecord> implements EmailRecordDao {
    @Override
    public int update(int id, String vendorEmailId) {
        Map<String, Object> map = new HashMap();
        map.put("id", id);
        map.put("vendor_email_id", vendorEmailId);

        return updateBy("update_vendor_email_id", map);
    }
}
