package com.cpvsn.mq.dao.impl;

import com.cpvsn.mq.dao.MailBaseDao;
import com.cpvsn.mq.dao.MqRetryMessageDao;
import com.cpvsn.mq.pojo.MqRetryMessage;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by pingli on 2021/5/11.
 */
@Component
public class MqRetryMessageDaoImpl extends MailBaseDao<MqRetryMessage> implements MqRetryMessageDao {
	public MqRetryMessageDaoImpl() {
	}

	public MqRetryMessage getById(String messageId) {
		Map<String, Object> map = new HashMap();
		map.put("message_id", messageId);
		return (MqRetryMessage)this.findUniqueBy("select_by_message_id", map);
	}
}