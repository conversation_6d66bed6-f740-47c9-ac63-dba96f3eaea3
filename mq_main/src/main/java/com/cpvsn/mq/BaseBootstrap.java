package com.cpvsn.mq;

import com.cpvsn.core.annotation.AnnotationScan;
import com.cpvsn.core.util.LogUtil;
import com.cpvsn.mq.common.SpringContext;
import com.cpvsn.mq.common.annotation.HandlerType;
import com.cpvsn.mq.common.config.MqConfig;
import com.cpvsn.mq.constant.QueueConst;
import com.cpvsn.mq.loader.ConfigLoader;
import com.cpvsn.mq.util.StringUtil;
import com.rabbitmq.client.ConnectionFactory;
import org.aeonbits.owner.ConfigFactory;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * 启动类
 * Created by cosx on 2015/10/18.
 */
@Component
public class BaseBootstrap {

    private final static Logger log = LogUtil.getLog(BaseBootstrap.class);

    protected CachingConnectionFactory factory;

    public void start() {
        MqConfig mqConfig = ConfigFactory.create(MqConfig.class);

        ConnectionFactory cf = new ConnectionFactory();
        cf.setHost(mqConfig.hostname());
        cf.setUsername(mqConfig.username());
        cf.setPassword(mqConfig.password());
        cf.setPort(mqConfig.port());

        factory = new CachingConnectionFactory(cf);

        loadHandler(factory);
    }

    private void loadHandler(CachingConnectionFactory factory) {
        AnnotationScan pp = AnnotationScan.getInstance();
        Set<Class<?>> set = pp.getPackageClass("com.cpvsn", HandlerType.class, true);
        for (Class<?> clazz : set) {
            HandlerType ht = clazz.getAnnotation(HandlerType.class);
            String routingKey = ht.routing_key();
            AcknowledgeMode ackMode = ht.ack_mode();
            int concurrentConsumers = ht.concurrent_consumers();
            routingKey = QueueConst.build_route_key(routingKey);

            try {
                receive(factory, routingKey, ackMode, concurrentConsumers,
                        SpringContext.getApplicationContext().getBean(clazz));
            } catch (Exception e) {
                log.error("build error!", e);
            }
        }
    }

    /**
     * 接收异步消息(手动确认)
     */
    protected void receive(CachingConnectionFactory factory, String queueName, AcknowledgeMode acknowledgeMode,
                           int concurrentConsumers, Object listener) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(factory);
        container.setConcurrentConsumers(concurrentConsumers);
        //设置消息处理完成的确认模式
        container.setAcknowledgeMode(acknowledgeMode);

        MessageListenerAdapter adapter = new MessageListenerAdapter(listener);

        container.setMessageListener(adapter);
        container.setQueueNames(queueName);
        container.start();

        log.info("[Bootstrap] start receive {}", queueName);
    }

}
