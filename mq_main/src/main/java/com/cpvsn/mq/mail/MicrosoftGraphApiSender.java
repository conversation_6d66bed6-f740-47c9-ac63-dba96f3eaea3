package com.cpvsn.mq.mail;


import com.azure.identity.AzureAuthorityHosts;
import com.azure.identity.ClientSecretCredential;
import com.azure.identity.ClientSecretCredentialBuilder;
import com.cpvsn.core.factory.CoreFactory;
import com.cpvsn.core.service.RedisService;
import com.cpvsn.core.util.LogUtil;
import com.cpvsn.mq.common.model.email.EmailAttModel;
import com.cpvsn.mq.common.model.email.EmailModel;
import com.cpvsn.mq.config.MainConfig;
import com.cpvsn.mq.config.MicrosoftGraphApiConfig;
import com.cpvsn.mq.constant.MailConst;
import com.cpvsn.rpc.service.common.util.DateUtil;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import com.microsoft.graph.authentication.TokenCredentialAuthProvider;
import com.microsoft.graph.core.DateOnly;
import com.microsoft.graph.models.*;
import com.microsoft.graph.requests.*;
import org.aeonbits.owner.ConfigFactory;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.tika.Tika;
import org.slf4j.Logger;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class MicrosoftGraphApiSender {

    private final static Logger log = LogUtil.getLog(MicrosoftGraphApiSender.class);
    private final static GraphServiceClient graphClient;

    private final static RedisService redisService = CoreFactory.getRedis();
    private final static MainConfig mainConfig = ConfigFactory.create(MainConfig.class);
    private final static String ID;


    static {
        MicrosoftGraphApiConfig config = ConfigFactory.create(MicrosoftGraphApiConfig.class);
        final ClientSecretCredential clientSecretCredential = new ClientSecretCredentialBuilder()
                .clientId(config.client_id())
                .clientSecret(config.client_secret())
                .tenantId(config.tenant_id())
                .authorityHost(AzureAuthorityHosts.AZURE_CHINA)
                .build();
        final TokenCredentialAuthProvider authProvider = new TokenCredentialAuthProvider(Collections.singletonList(config.api_prefix() + ".default"), clientSecretCredential);
        graphClient = GraphServiceClient.builder().authenticationProvider(authProvider).buildClient();
        graphClient.setServiceRoot(config.api_prefix() + "v1.0");

        ID = config.extend_property_id();
    }

    /**
     * 限制一个账户三秒内只能发送一封邮件
     */
    private static void checkSender(String sender) throws InterruptedException {
        /** 设置同一个邮件账号发送邮件的间隔不能小于2秒，一般邮件服务器发送邮件需要两秒，如果短时间内大量邮件，
         * 就会导致office365邮件服务器邮件队列堆积，队列堆积，邮件服务器发送邮件的速度会降低,考虑到程序执行时间，设置为间隔3秒  **/
        int count = 0;
        while (redisService.set_nx(sender, "1") != 1 && count < 20) {
            Thread.sleep(1000);
            /** 添加一个count， 最多尝试20次设置，如果超过20则直接跳过，超过20次可能是因为上一次redis set nx成功，但是设置过期时间失败，导致key对应的值一直存在，无法set nx成功， 加一个count限制，可以防止这种情况一直循环下去 **/
            count++;
        }

        redisService.expire(sender, 3);
    }

    public static void sendEmailWithAttachments(EmailModel model) throws Exception {
        Message message = new Message();

        // 如果是发件人是邮件群组地址，则必须是用其他user 邮件地址代理发送，不能直接用邮件群组发送，因为邮件群组不是user，不能发送邮件
        String sender = model.getFrom();
        String senderName = model.getFromName();
        if (isGroupEmail(model.getFrom())) {
            sender = mainConfig.proxy_send_email_default();
            senderName = MailConst.SYSTEM_EMAIL_NAME;
        }

        message.sender = buildRecipient(sender, senderName);
        message.from = buildRecipient(model.getFrom(), model.getFromName());
        message.toRecipients = buildRecipientList(model.getTo());
        message.ccRecipients = buildRecipientList(model.getCc());
        message.bccRecipients = buildRecipientList(model.getBcc());
        message.body = buildBody(model.getContent());
        message.subject = model.getSubject();

        if (StringUtils.isNotEmpty(model.getMessage_id())) {
            message.internetMessageId = model.getMessage_id();
        }
        // 目前Graph API不支持设置 In-Reply-To,只能通过使用reply的API进行回复
//        if (StringUtils.isNotEmpty(model.getReply_message_id())) {
//            InternetMessageHeader replyTo = new InternetMessageHeader();
//            replyTo.name = "In-Reply-To";
//            replyTo.value = model.getReply_message_id();
//            message.internetMessageHeaders = new ArrayList<>();
//            message.internetMessageHeaders.add(replyTo);
//        }
        if (CollectionUtils.isEmpty(model.getReply_to_list())) {
            message.replyTo = buildRecipientList(model.getReply_to_list());
        }

        AttachmentCollectionPage page = buildAttachments(model.getAttList());
        if (page != null) {
            message.attachments = page;
            message.hasAttachments = true;
        }
        if (MailConst.SYSTEM_EMAIL.equals(sender) || MailConst.DB_SENDER_MAIL.equals(sender)) {
            sender = ExchangeSender.getSender();
        }
        checkSender(sender);
        graphClient.users(sender).sendMail(UserSendMailParameterSet.newBuilder().withMessage(message)
                .withSaveToSentItems(true).build()).buildRequest().post();
    }

    public static void sendCalendar(EmailModel model) throws ParseException, IOException, InterruptedException {
        Event event = new Event();
        event.subject = model.getSubject();
        event.body = buildBody(model.getContent());
        event.attendees = Stream.concat(buildCalendarAttendee(model.getTo(), AttendeeType.REQUIRED).stream(),
                buildCalendarAttendee(model.getOptional_to(), AttendeeType.OPTIONAL).stream()).collect(Collectors.toList());
        event.start = buildCalendarTime(model.getStartTime(), com.cpvsn.mq.util.DateUtil.US_TIMEZONE);
        event.end = buildCalendarTime(model.getEndTime(), com.cpvsn.mq.util.DateUtil.US_TIMEZONE);
        event.recurrence = buildPatternedRecurrence(model);
        event.reminderMinutesBeforeStart = 15;
        event.isOrganizer = true;
        event.isReminderOn = true;
        event.organizer = buildRecipient(model.getFrom(), model.getFromName());
        event.isOnlineMeeting = false;
        Location location = new Location();
        location.displayName = model.getLocation();
        event.location = location;
        event.transactionId = model.getCalendarUid();
        event.singleValueExtendedProperties = buildExtraProperties(model);

        Event oldEvent = getEvent(model);
        Event entity = null;
        checkSender(model.getFrom());
        // 如果日历已经发送过，更新日历必须用patch方法
        if (oldEvent != null) {
            EventRequest request = graphClient.users(model.getFrom()).events(oldEvent.id).buildRequest();
            request.addHeader("Prefer", "IdType=\"ImmutableId\"");
            entity = request.patch(event);
        } else {
            EventCollectionRequest request = graphClient.users(model.getFrom()).events().buildRequest();
            request.addHeader("Prefer", "IdType=\"ImmutableId\"");
            entity = request.post(event);
        }

        log.info("[MicrosoftGraphApiSender] send calendar ok. event_id: {}", entity.id);
    }

    public static void cancelCalendar(EmailModel model) throws InterruptedException {
        Event oldEvent = getEvent(model);
        if (oldEvent == null) {
            throw new RuntimeException("We can not cancel a not exist Calendar.");
        }
        checkSender(model.getFrom());
        graphClient.users(model.getFrom()).events(oldEvent.id).cancel(
                EventCancelParameterSet.newBuilder().withComment("cancel calendar").build()
        ).buildRequest().post();
    }

    private static List<Recipient> buildRecipientList(List<String> mailAddressList) {
        return (List) ((Stream) Optional.ofNullable(mailAddressList).map(Collection::stream).orElse(Stream.empty())).map(m -> buildRecipient((String)m, null)).collect(Collectors.toList());
    }

    private static Recipient buildRecipient(String mailAddress, String name) {
        Recipient recipient = new Recipient();
        recipient.emailAddress = buildEmailAddress(mailAddress, name);

        return recipient;
    }

    private static EmailAddress buildEmailAddress(String mail, String name) {
        EmailAddress address = new EmailAddress();
        address.address = mail;
        address.name = name;

        return address;
    }

    private static ItemBody buildBody(String content) throws IOException {
        ItemBody body =  new ItemBody();
        body.contentType = BodyType.HTML;
                /** 使用addInline 的 cid:contentId(<img alt="Capvision Logo" src="cid:logo.png">) 添加 html 元素中的src属性中对应的文件, 这样加载图片，会比直接使用url的图片，直接显示url的图片在邮件不会直接显示，会不安全 **/
        if (content.contains("src=\"cid:logo.png\"")) {
            content = content.replace("src=\"cid:logo.png\"", getSrcFileBase64Data("img/logo.png"));
        }
        if (content.contains("src=\"cid:project_feedback.jpg\"")) {
            content = content.replace("src=\"cid:project_feedback.jpg\"", getSrcFileBase64Data("img/project_feedback.jpg"));
        }

        body.content = content;

        return body;
    }

    private static String getSrcFileBase64Data(String filename) throws IOException {
        Tika tika = new Tika();
        ClassPathResource resource = new ClassPathResource(filename);
        return String.format("src=\"data:%s;base64,%s\"", tika.detect(resource.getInputStream()),
                Base64.encodeBase64String(IOUtils.toByteArray(resource.getInputStream())));
    }

    private static AttachmentCollectionPage buildAttachments(List<EmailAttModel> attList) throws UnirestException, IOException {
        if (CollectionUtils.isEmpty(attList)) {
            return null;
        }

        AttachmentCollectionResponse attachmentCollectionResponse = new AttachmentCollectionResponse();
        List<Attachment> attachmentList = new ArrayList<>();
        attachmentCollectionResponse.value = attachmentList;

        for (EmailAttModel attModel : attList) {
            FileAttachment fileAttachment = new FileAttachment();
            fileAttachment.oDataType = "#microsoft.graph.fileAttachment";

            fileAttachment.contentBytes = attModel.getAtt();
            fileAttachment.contentType = attModel.getAttType();
            fileAttachment.name = attModel.getAttName();

            attachmentList.add(fileAttachment);
        }

        return new AttachmentCollectionPage(attachmentCollectionResponse, null);
    }

    /**
     * 判断一个邮件地址是不是邮件群组
     * @param mail
     * @return
     */
    private static boolean isGroupEmail(String mail) {
        GroupCollectionPage response = graphClient.groups().buildRequest().filter(String.format("mail eq '%s' or proxyAddresses/any(i:i eq 'smtp:%s') ", mail, mail)).count(true).select("id, displayName").get();
        if (response != null && !CollectionUtils.isEmpty(response.getCurrentPage())) {
            return true;
        }

        return false;
    }

    private static List<Attendee> buildCalendarAttendee(List<String> emailList, AttendeeType attendeeType) {
        return (List) ((Stream) Optional.ofNullable(emailList).map(Collection::stream).orElse(Stream.empty())).map(e -> {
            Attendee attendee = new Attendee();
            attendee.emailAddress = buildEmailAddress((String) e, null);
            attendee.type = attendeeType;

            return attendee;
        }).collect(Collectors.toList());
    }

    private static DateTimeTimeZone buildCalendarTime(Date date, String timeZone) {
        DateTimeTimeZone dateTimeZone = new DateTimeTimeZone();
        dateTimeZone.timeZone = timeZone;
        dateTimeZone.dateTime = DateUtil.dateFormatter(date, "yyyy-MM-dd'T'HH:mm:ss");

        return dateTimeZone;
    }


    /**
     * 设置重复日历的定期模式
     * @return
     */
    private static PatternedRecurrence buildPatternedRecurrence(EmailModel model) throws ParseException {
        if (StringUtils.isEmpty(model.getRepeatRule())) {
            return null;
        }

        PatternedRecurrence patternedRecurrence = new PatternedRecurrence();
        RecurrencePattern pattern = new RecurrencePattern();
        pattern.interval = model.getInterval();
        switch (EmailModel.RepeatRule.valueOf(model.getRepeatRule())) {
            case DAILY:
                pattern.type = RecurrencePatternType.DAILY;
                break;
            case WEEKLY:
                pattern.type = RecurrencePatternType.WEEKLY;
                pattern.daysOfWeek = model.getDaysOfWeek().stream().map(d->DayOfWeek.valueOf(d.name())).collect(Collectors.toList());
                pattern.firstDayOfWeek = DayOfWeek.valueOf(model.getFirstDayOfWeek().name());
                break;
            case ABSOLUTE_MONTHLY:
                pattern.type = RecurrencePatternType.ABSOLUTE_MONTHLY;
                pattern.dayOfMonth = model.getDayOfMonth();
                break;
            case RELATIVE_MONTHLY:
                pattern.type = RecurrencePatternType.RELATIVE_MONTHLY;
                pattern.daysOfWeek = model.getDaysOfWeek().stream().map(d->DayOfWeek.valueOf(d.name())).collect(Collectors.toList());
                pattern.index = WeekIndex.valueOf(model.getWeekIndex().name());
                break;
            case ABSOLUTE_YEARLY:
                pattern.dayOfMonth = model.getDayOfMonth();
                pattern.month = model.getMonth();
                pattern.type = RecurrencePatternType.ABSOLUTE_YEARLY;
                break;
            case RELATIVE_YEARLY:
                pattern.type = RecurrencePatternType.RELATIVE_YEARLY;
                pattern.daysOfWeek = model.getDaysOfWeek().stream().map(d->DayOfWeek.valueOf(d.name())).collect(Collectors.toList());
                pattern.month = model.getMonth();
                pattern.index = WeekIndex.valueOf(model.getWeekIndex().name());
                break;
            default:
                return null;
        }

        RecurrenceRange range = new RecurrenceRange();
        range.startDate = DateOnly.parse(DateUtil.dateFormatter(model.getStartTime(), "yyyy-MM-dd"));
        range.type = RecurrenceRangeType.NO_END;
        if (model.getRepeatEndTime() != null) {
            range.type = RecurrenceRangeType.END_DATE;
            range.endDate = DateOnly.parse(DateUtil.dateFormatter(model.getRepeatEndTime(), "yyyy-MM-dd"));
        }

        patternedRecurrence.pattern = pattern;
        patternedRecurrence.range = range;

        return patternedRecurrence;
    }

    /**
     * 定义保存扩展属性Calendar id，用于后续通过扩展属性查找日历是否已经发送过
     */
    private static SingleValueLegacyExtendedPropertyCollectionPage buildExtraProperties(EmailModel model) {
        final SingleValueLegacyExtendedProperty prop = new SingleValueLegacyExtendedProperty();
//        prop.id = ID;
        prop.propertyId = ID;
        prop.value = model.getCalendarUid();
        final SingleValueLegacyExtendedPropertyCollectionResponse response = new SingleValueLegacyExtendedPropertyCollectionResponse();
        response.value = new ArrayList<SingleValueLegacyExtendedProperty>();
        response.value.add(prop);
        return new SingleValueLegacyExtendedPropertyCollectionPage(response,
                new SingleValueLegacyExtendedPropertyCollectionRequestBuilder("", graphClient, null));
    }

    /**
     * 判断日历是否发送过
     */
    private static Event getEvent(EmailModel model) {
        EventCollectionPage response = graphClient.users(model.getFrom()).events().buildRequest()
                .filter(String.format("singleValueExtendedProperties/Any(ep: ep/Id  eq '%s' and ep/value eq '%s')", ID, model.getCalendarUid()))
                .select("subject,body,start,end,location,id,TransactionId,SingleValueExtendedProperties").get();

        if (response != null && !CollectionUtils.isEmpty(response.getCurrentPage())) {
            return response.getCurrentPage().stream().filter(e-> e.transactionId.equals(model.getCalendarUid())).findFirst().orElse(null);
        }

        return null;
    }

}
