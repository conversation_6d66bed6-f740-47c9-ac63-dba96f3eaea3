package com.cpvsn.mq.mail;

import com.cpvsn.core.util.LogUtil;
import org.slf4j.Logger;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;

import javax.mail.internet.MimeMessage;

/**
 * Created by cosx on 2015/10/15.
 */
public class BaseMailSender {

    protected final static Logger log = LogUtil.getLog(BaseMailSender.class);

    protected static JavaMailSenderImpl sender = new JavaMailSenderImpl();

    static {
        /** Sender Initial **/
    }

    /**
     * 发送邮件with附件
     * @param from 发件人
     * @param toList 收件人
     * @param subject 主题
     * @param content 内容
     * @param filename 文件名
     * @param fileType 文件类型
     * @param att 附件
     */
    public static boolean sendWithAtt(
            String from, String[] toList, String[] ccList, String[] bccList, String subject, String content,
            String filename, String fileType, byte[] att) {
        MimeMessage message = sender.createMimeMessage();

        try {
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom(from);
            helper.setSubject(subject);
            helper.setText(content, true);
            helper.setTo(toList);
            helper.setCc(ccList);
            helper.setBcc(bccList);

            helper.addAttachment(filename, new ByteArrayResource(att, fileType));

            sender.send(message);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Send Mail Error", e);
        }

        return true;
    }
}
