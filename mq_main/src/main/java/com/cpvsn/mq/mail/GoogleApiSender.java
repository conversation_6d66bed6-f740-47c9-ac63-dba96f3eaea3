package com.cpvsn.mq.mail;

import com.cpvsn.mq.common.model.email.EmailAttModel;
import com.cpvsn.mq.common.model.email.EmailModel;
import com.cpvsn.mq.util.DateUtil;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.util.DateTime;
import com.google.api.services.calendar.model.EventAttendee;
import com.google.api.services.calendar.model.EventDateTime;
import com.google.api.services.calendar.model.Events;
import com.google.auth.oauth2.ServiceAccountCredentials;
import com.google.auth.http.HttpCredentialsAdapter;
import com.google.api.services.gmail.Gmail;
import com.google.api.services.gmail.model.Message;
import com.google.api.services.calendar.Calendar;
import com.google.api.services.calendar.model.Event;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.jackson2.JacksonFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.util.CollectionUtils;
import org.apache.commons.codec.binary.Base64;

import javax.mail.*;
import java.io.*;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;



public class GoogleApiSender {
    private static final String APPLICATION_NAME = "Gmail API Java";
    private static final String SERVICE_ACCOUNT_PATH = "certs/google_client_cert.json";
    private static final String CALENDAR_ID = "primary";
    private static final String EVENT_SEND_UPDATE = "all";
    private static final List<String> SCOPES = Arrays.asList("https://www.googleapis.com/auth/gmail.send", "https://www.googleapis.com/auth/calendar", "https://www.googleapis.com/auth/admin.directory.user.readonly", "https://www.googleapis.com/auth/admin.directory.group.readonly");


    private static final ServiceAccountCredentials googleCredential;

    static {
        try {
            googleCredential = (ServiceAccountCredentials) ServiceAccountCredentials.fromStream(new ClassPathResource(SERVICE_ACCOUNT_PATH).getInputStream())
                    .createScoped(SCOPES);

            googleCredential.refreshIfExpired();  // 如果 token 已过期则刷新


        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private static Gmail getGmailService(String sender) {
        return new Gmail.Builder(new NetHttpTransport(), JacksonFactory.getDefaultInstance(), new HttpCredentialsAdapter(googleCredential.createDelegated(sender)))
                .setApplicationName(APPLICATION_NAME)
                .build();
    }

    private static Calendar getCalendarService(String sender) {
        return new Calendar.Builder(new NetHttpTransport(), JacksonFactory.getDefaultInstance(), new HttpCredentialsAdapter(googleCredential.createDelegated(sender)))
                .setApplicationName(APPLICATION_NAME)
                .build();
    }


    // 发送 Gmail 邮件
    public static void sendEmailWithAttachment(EmailModel model) throws Exception {
        MimeMessage message = new MimeMessage(Session.getDefaultInstance(System.getProperties(), null));

        MimeMessageHelper helper = ExchangeSender.buildMessageHelper(model.getFrom(), model.getFromName(), model.getTo().toArray(new String[model.getTo().size()]),
                model.getCc().toArray(new String[model.getCc().size()]), model.getBcc().toArray(new String[model.getBcc().size()]),
                model.getSubject(), model.getContent(), message);

        if (!CollectionUtils.isEmpty(model.getAttList())) {
            for (EmailAttModel eam : model.getAttList()) {
                helper.addAttachment(eam.getAttName(), new ByteArrayResource(eam.getAtt(), eam.getAttType()));
            }
        }

        /** 如果要让邮件显示在同一邮件链上（即类似在outlook邮件中直接回复转发的邮件可以自动折叠起来）则需要设置本次message id和 上一封邮件（原邮件)的id reply_message_id**/
        if (StringUtils.isNotEmpty(model.getMessage_id())) {
            message.setHeader("Message-Id", model.getMessage_id());
        }
        if (StringUtils.isNotEmpty(model.getReply_message_id())) {
            message.setHeader("In-Reply-To", model.getReply_message_id());
        }

        // 将邮件转换为 Base64 编码的字符串
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        message.writeTo(byteArrayOutputStream);
        String encodedEmail = Base64.encodeBase64String(byteArrayOutputStream.toByteArray());

        Message emailMessage = new Message();
        emailMessage.setRaw(encodedEmail);

        getGmailService(model.getFrom()).users().messages().send(model.getFrom(), emailMessage).execute();
    }

    public static void sendCalendar(EmailModel model) throws Exception {
        Calendar calendarService = getCalendarService(model.getFrom());
        Event event = createEvent(model);
        // 查询日历是否存在,如果日历存在则更新
        Event oldEvent = getEvent(model, calendarService);
        if (oldEvent != null) {
            calendarService.events().update(CALENDAR_ID, oldEvent.getId(), event).setSendUpdates(EVENT_SEND_UPDATE).execute();
        } else {
            calendarService.events().insert(CALENDAR_ID, event).setSendUpdates(EVENT_SEND_UPDATE).execute();
        }
    }

    public static void cancelCalendar(EmailModel model) throws Exception {
        Calendar calendarService = getCalendarService(model.getFrom());
        Event event = getEvent(model, calendarService);
        if (event != null) {
            calendarService.events().delete(CALENDAR_ID, event.getId()).setSendUpdates(EVENT_SEND_UPDATE).execute();
        }
    }

    public static Event getEvent(EmailModel model, Calendar calendarService) throws IOException {
        Events events = calendarService.events().list(CALENDAR_ID).setPrivateExtendedProperty(Collections.singletonList(String.format("calendarUid=%s", model.getCalendarUid()))).execute();
        return !events.getItems().isEmpty() ? events.getItems().get(0) : null;
    }


    private static Event createEvent(EmailModel model) {
        Map<String, String> privateExtendedPropertiesMap = new HashMap<>();
        privateExtendedPropertiesMap.put("calendarUid", model.getCalendarUid());
        Event event = new Event()
                .setSummary(model.getSubject())
                .setLocation(model.getLocation())
                .setDescription(model.getContent())
                .setSequence(model.getSequence())
                .setExtendedProperties(new Event.ExtendedProperties().setPrivate(privateExtendedPropertiesMap))
                .setStart(new EventDateTime().setDateTime(new DateTime(DateUtil.date_str(model.getStartTime(), DateUtil.SDF_FULL_TIMEZONE))).setTimeZone(DateUtil.US_TIMEZONE))
                .setEnd(new EventDateTime().setDateTime(new DateTime(DateUtil.date_str(model.getEndTime(), DateUtil.SDF_FULL_TIMEZONE))).setTimeZone(DateUtil.US_TIMEZONE))
                .setOrganizer(new Event.Organizer().setEmail(model.getFrom()))
                .setAttendees(Stream.concat(buildAttendeeList(model.getTo(), false).stream(), buildAttendeeList(model.getOptional_to(), true).stream()).collect(Collectors.toList()));

        return event;
    }

    private static List<EventAttendee> buildAttendeeList(List<String> emailList, boolean isOptional) {
        return Optional.ofNullable(emailList).orElse(Collections.emptyList()).stream().map(m -> new EventAttendee().setEmail(m)).collect(Collectors.toList());
    }






}
