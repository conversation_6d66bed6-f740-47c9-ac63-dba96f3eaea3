package com.cpvsn.mq.mail;

import com.cpvsn.core.factory.CoreFactory;
import com.cpvsn.core.service.RedisService;
import com.cpvsn.core.util.LogUtil;
import com.cpvsn.mq.constant.MailConst;
import com.cpvsn.mq.common.exception.MqMailException;
import com.cpvsn.mq.common.model.email.EmailAttModel;
import com.cpvsn.mq.common.model.email.EmailModel;
import com.cpvsn.mq.common.util.ExceptionUtil;
import com.cpvsn.mq.loader.ConfigLoader;
import com.cpvsn.mq.util.DateUtil;
import com.cpvsn.mq.util.StringUtil;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import net.fortuna.ical4j.data.CalendarOutputter;
import net.fortuna.ical4j.model.DateTime;
import net.fortuna.ical4j.model.Dur;
import net.fortuna.ical4j.model.TimeZoneRegistry;
import net.fortuna.ical4j.model.TimeZoneRegistryFactory;
import net.fortuna.ical4j.model.component.VAlarm;
import net.fortuna.ical4j.model.component.VEvent;
import net.fortuna.ical4j.model.component.VTimeZone;
import net.fortuna.ical4j.model.parameter.Cn;
import net.fortuna.ical4j.model.parameter.PartStat;
import net.fortuna.ical4j.model.parameter.Role;
import net.fortuna.ical4j.model.parameter.Rsvp;
import net.fortuna.ical4j.model.property.*;
import net.fortuna.ical4j.util.CompatibilityHints;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.mail.MailSendException;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;

import javax.mail.Message;
import javax.mail.Multipart;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import java.io.*;
import java.net.URI;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by cosx on 2015/10/15.
 */
public class ExchangeSender {

    private final static Logger log = LogUtil.getLog(ExchangeSender.class);

    private final static JavaMailSenderImpl sender = new JavaMailSenderImpl();

    private final static List<String> SENDER_COMMON_LIST = new ArrayList<>();
    private final static RedisService redisService = CoreFactory.getRedis();

    private static int LAST_INDEX = 0;

    static {
        sender.setHost("smtp.partner.outlook.cn");
        sender.setPort(587);
        sender.setUsername(ConfigLoader.getMainConfig().proxy_send_email_default());
        sender.setPassword(ConfigLoader.getMainConfig().proxy_send_email_password());

        Properties p = new Properties();
        p.setProperty("mail.smtp.sendpartial", "true");
        p.setProperty("mail.smtp.auth", "true");
        p.setProperty("mail.smtp.starttls.enable", "true");
        p.setProperty("mail.smtp.ssl.trust", "*");
        p.setProperty("mail.smtp.ssl.protocols", "TLSv1.2"); // microsoft 从2022开始要使用tls1.2，继续使用tls1.0,1.11会拒绝一部分服务，并且随着时间越多，拒绝越多

        p.setProperty("mail.smtp.connectiontimeout", "10000");
        p.setProperty("mail.smtp.timeout", "50000");
        p.setProperty("mail.smtp.writetimeout", "30000");

        sender.setJavaMailProperties(p);

        SENDER_COMMON_LIST.addAll(Arrays.asList(ConfigLoader.getMainConfig().proxy_send_email_list().split(",")));
    }

    public static String getSender() throws Exception {
        LAST_INDEX++;
        /** 防止超出int上限值 **/
        if (LAST_INDEX >= 1000000) {
            LAST_INDEX = 0;
        }

        String user = SENDER_COMMON_LIST.get(LAST_INDEX % SENDER_COMMON_LIST.size());
        int count = 0;

        /** 设置同一个邮件账号发送邮件的间隔不能小于2秒，一般邮件服务器发送邮件需要两秒，如果短时间内大量邮件，
         * 就会导致office365邮件服务器邮件队列堆积，队列堆积，邮件服务器发送邮件的速度会降低,考虑到程序执行时间，设置为间隔3秒  **/
        while (redisService.set_nx(user, "1") != 1 && count < 10) {
            Thread.sleep(1000);
            /** 添加一个count， 最多尝试20次设置，如果超过20则直接跳过，超过20次可能是因为上一次redis set nx成功，但是设置过期时间失败，导致key对应的值一直存在，无法set nx成功， 加一个count限制，可以防止这种情况一直循环下去 **/
            count++;
        }

        redisService.expire(user, 3);


        log.info("[exchange_sender]: " + sender.getUsername());
        return user;
    }

    /**
     * 发送邮件
     *
     * @param from    发件人
     * @param toList  收件人
     * @param subject 主题
     * @param content 内容
     */
    public static boolean send(String from, String fromName, String[] toList, String[] ccList, String[] bccList, String subject, String content) throws Exception{
        MimeMessage message = sender.createMimeMessage();
        MimeMessageHelper helper = buildMessageHelper(from, fromName, toList, ccList, bccList, subject, content, message);

        sendMail(message);

        return true;
    }

    public static boolean send(String from, String fromName, String[] toList, String subject, String content) throws Exception{
        return send(from, fromName, toList, null, null, subject, content);
    }

    /**
     * 发送邮件带附件
     *
     * @param from    发件人
     * @param toList  收件人
     * @param subject 主题
     * @param content 内容
     * @param attList 附件列表
     */
    public static boolean sendWithAtt(
            int sender_type,
            String from, String fromName,
            String[] toList, String[] ccList, String[] bccList,
            String subject, String content, List<EmailAttModel> attList, EmailModel model) throws Exception{
        MimeMessage message = sender.createMimeMessage();
        message.setHeader("Message-ID", "SHXPR01MB0670" +
                UUID.randomUUID().toString().replace("-", "").toUpperCase() + "@SHXPR01MB0670.CHNPR01.prod.partner.outlook.cn");
        /** 如果要让邮件显示在同一邮件链上（即类似在outlook邮件中直接回复转发的邮件可以自动折叠起来）则需要设置本次message id和 上一封邮件（原邮件)的id reply_message_id**/
        if (StringUtils.isNotEmpty(model.getMessage_id())) {
            message.setHeader("Message-Id", model.getMessage_id());
        }
        if (StringUtils.isNotEmpty(model.getReply_message_id())) {
            message.setHeader("In-Reply-To", model.getReply_message_id());
        }

        MimeMessageHelper helper = buildMessageHelper(from, fromName, toList, ccList, bccList, subject, content, message);

        if (attList != null && attList.size() > 0) {
            Set<String> sendPath = new HashSet<>();
            for (EmailAttModel eam : attList) {
                if (!StringUtils.isEmpty(eam.getFilePath())) { //remove dup
                    if (sendPath.contains(eam.getFilePath())) {
                        continue;
                    }
                    sendPath.add(eam.getFilePath());
                }

                buildEmailAtt(eam, helper);
            }
        }

        sendMail(message);

        return true;
    }

    public static void sendEventEmail(String calendarUid, int sequence, String from, String fromName,
                                      List<String> reqs,
                                      List<String> cc,
                                      Date startTime, Date endTime,
                                      String location, String name, String content)
            throws Exception {
        if (reqs == null || reqs.isEmpty()) {
            throw new IllegalArgumentException(
                    "Required participant should not be empty!");
        }

        /**以下两步骤的处理也是为了防止outlook或者是notes将日历当做附件使用增加的*/
        CompatibilityHints.setHintEnabled(CompatibilityHints.KEY_OUTLOOK_COMPATIBILITY, true);
        CompatibilityHints.setHintEnabled(CompatibilityHints.KEY_NOTES_COMPATIBILITY, true);

        // 设置时区
        TimeZoneRegistry registry = TimeZoneRegistryFactory.getInstance()
                .createRegistry();
        net.fortuna.ical4j.model.TimeZone timeZone = registry.getTimeZone(DateUtil.US_TIMEZONE);
        VTimeZone tz = timeZone.getVTimeZone();

        DateTime start = new DateTime(startTime);
        start.setUtc(true);
        DateTime end = new DateTime(endTime);
        end.setUtc(true);
        VEvent meeting = new VEvent(start, end, name);
        meeting.getProperties().add(tz.getTimeZoneId());

        meeting.getProperties().add(new Location(location));
        /** 日历中去掉html格式标签 **/
        meeting.getProperties().add(new Description(
                content.replaceAll("\\r\\n", "")
                        .replaceAll("<br />", "")
                        .replaceAll("\t", "")
                        .replaceAll("&nbsp;", "")
                        .replaceAll("<[^>]+>", "")));

        /** 添加支持html显示content *
         XProperty p = new XProperty("X-ALT-DESC");
         p.getParameters().add(new FmtType("text/html"));
         p.setValue(content);
         meeting.getProperties().add(p);
         */

        // 设置uid
        meeting.getProperties().add(new Uid(calendarUid));
        meeting.getProperties().add(new Sequence(String.valueOf(sequence)));

        /** 添加组织者 **/
        Organizer organizer = new Organizer(URI.create("mailto:"
                + from));
        organizer.getParameters().add(new Cn(fromName));
        meeting.getProperties().add(organizer);
        /** 添加参与者 **/
        createAttendeeList(reqs, meeting);
        meeting.getProperties().add(createEventAttendee(from, fromName));

        setCalendarXProperty(meeting,sequence,false);

        //提前10分钟提醒
        VAlarm valarm = new VAlarm(new Dur(0, 0, -10, 0));
        valarm.getProperties().add(new Repeat(1));
        valarm.getProperties().add(new Duration(new Dur(0, 0, 10, 0)));

        net.fortuna.ical4j.model.Calendar icsCalendar = new net.fortuna.ical4j.model.Calendar();
        icsCalendar.getProperties().add(
                new ProdId("Microsoft Exchange Server 2010"));
        icsCalendar.getProperties().add(Version.VERSION_2_0);
        icsCalendar.getProperties().add(Method.REQUEST);
        icsCalendar.getComponents().add(meeting);
        /** 下面这个语句不能添加，否则会导致时区信息有问题，邮箱客户端无法识别该日历，变成附件 **/
        /*icsCalendar.getComponents().add(tz);*/

        CalendarOutputter co = new CalendarOutputter(false);
        Writer wtr = new StringWriter();
        co.output(icsCalendar, wtr);
        String mailContent = wtr.toString();

        sendCalendarEmail(from, fromName, reqs, cc, new ArrayList<String>(), name, mailContent, content, false);
    }

    public static void cancelEventEmail(String calendarUid, int sequence, String from, String fromName,
                                        List<String> reqs,
                                        List<String> cc,
                                        Date startTime, Date endTime,
                                        String location, String name, String content)
            throws Exception {
        // 设置时区
        TimeZoneRegistry registry = TimeZoneRegistryFactory.getInstance()
                .createRegistry();
        net.fortuna.ical4j.model.TimeZone timeZone = registry.getTimeZone(DateUtil.US_TIMEZONE);
        VTimeZone tz = timeZone.getVTimeZone();

        DateTime start = new DateTime(startTime);
        start.setUtc(true);
        DateTime end = new DateTime(endTime);
        end.setUtc(true);
        VEvent meeting = new VEvent(start, end, name);
        meeting.getProperties().add(tz.getTimeZoneId());

        meeting.getProperties().add(new Location(location));
        /** 日历中去掉html格式标签 **/
        meeting.getProperties().add(new Description(
                content.replaceAll("\\r\\n", "n")
                        .replaceAll("<br />", "")
                        .replaceAll("\t", "")
                        .replaceAll("&nbsp;", "")
                        .replaceAll("<[^>]+>", "")));

        /** 添加支持html显示content *
         XProperty p = new XProperty("X-ALT-DESC");
         p.getParameters().add(new FmtType("text/html"));
         p.setValue(content);
         meeting.getProperties().add(p);*/

        // 设置uid
        meeting.getProperties().add(new Uid(calendarUid));
        meeting.getProperties().add(Status.VEVENT_CANCELLED);
        meeting.getProperties().add(new Sequence(String.valueOf(sequence)));

        /** 添加组织者 **/
        Organizer organizer = new Organizer(URI.create("mailto:"
                + from));
        organizer.getParameters().add(new Cn(fromName));
        meeting.getProperties().add(organizer);
        /** 添加参与者 **/
        createAttendeeList(reqs, meeting);
        meeting.getProperties().add(createEventAttendee(from, fromName));

        setCalendarXProperty(meeting,sequence,true);

        net.fortuna.ical4j.model.Calendar icsCalendar = new net.fortuna.ical4j.model.Calendar();
        icsCalendar.getProperties().add(
                new ProdId("Microsoft Exchange Server 2010"));
        icsCalendar.getProperties().add(Version.VERSION_2_0);
        icsCalendar.getProperties().add(Method.CANCEL);
        icsCalendar.getComponents().add(meeting);
        /*icsCalendar.getComponents().add(tz);*/

        CalendarOutputter co = new CalendarOutputter(false);
        Writer wtr = new StringWriter();
        co.output(icsCalendar, wtr);
        String mailContent = wtr.toString();

        sendCalendarEmail(from, fromName, reqs, cc, new ArrayList<String>(), name, mailContent, content, true);
    }

    public static void sendCalendarEmail(String from, String fromName,
                                         List<String> tos,
                                         List<String> ccList,
                                         List<String> bccList,
                                         String subject, String calendarContent, String htmlContent, boolean is_cancel) throws Exception {
        try {
            MimeMessage message = sender.createMimeMessage();
            message.setHeader("Message-ID", "SHXPR01MB0670" +
                    UUID.randomUUID().toString().replace("-", "").toUpperCase() + "@SHXPR01MB0670.CHNPR01.prod.partner.outlook.cn");
            message.setSubject(subject);
            for (String to : tos) {
                message.addRecipient(Message.RecipientType.TO, new InternetAddress(to));
            }
            for (String cc : ccList) {
                message.addRecipient(Message.RecipientType.CC, new InternetAddress(cc));
            }
            for (String bcc : bccList) {
                message.addRecipient(Message.RecipientType.BCC, new InternetAddress(bcc));
            }
            message.setFrom(new InternetAddress(from, fromName));

            /** 避免outlook将生成的日历项当做附件处理必须新建Multipart/alternative类型把内容当成纯文本或者超文本处理，
             * 如果不设置为alternative,默认的就是Multipart/mixed 在有些邮箱中日历会被当做附件处理    **/
            Multipart multipart = new MimeMultipart("alternative");
            MimeBodyPart iCalAttachment = new MimeBodyPart();
            String type = is_cancel
                    // https://answers.microsoft.com/en-us/msoffice/forum/all/cancelled-mail-ics-showing-not-supported/97636e5f-4f3c-404b-84ad-80fc74b5f1ae
                    ? "text/calendar;method=CANCEL;charset=UTF-8"
                    : "text/calendar;method=REQUEST;charset=UTF-8";
            iCalAttachment.setContent(calendarContent, type);
            iCalAttachment.setHeader("Content-Transfer-Encoding", "8bit");

            MimeBodyPart html = new MimeBodyPart();
            html.setContent("<!DOCTYPE><html><head><meta charset=\"UTF-8\"></head><body>" + htmlContent + "</body></html>", "text/html;charset=UTF-8");
            html.setHeader("Content-Transfer-Encoding", "quoted-printable");
            /** 先添加html再添加calendar，顺序不能反了，否则exchange邮箱可能会生成一个html附件 **/
            multipart.addBodyPart(html);
            multipart.addBodyPart(iCalAttachment);
            message.setHeader("Accept-Language", "en-US, zh-CN");
            message.setHeader("Content-Language", "zh-CN");
            message.setContent(multipart);

            sendMail(message);
        } catch (Exception e) {
            checkException(e);
        }

    }


    /**
     * 判断接受邮件的地址是否合法,并且去出地址中的空白字符
     */
    private static String[] checkMailAddress(String[] addressList) {
        if (addressList == null || addressList.length == 0) {
            return addressList;
        }

        int length = addressList.length;
        for (int i = 0; i < length; i++) {
            if (StringUtils.isEmpty(addressList[i])) {
                throw new MqMailException("Mail address contain null.");
            }
            addressList[i] = StringUtil.removeTrim(addressList[i]);
        }

        return addressList;
    }

    public static MimeMessageHelper buildMessageHelper(String from, String fromName,
                                                        String[] toList, String[] ccList, String[] bccList,
                                                        String subject, String content, MimeMessage message) throws Exception {
        String[] fromArray = new String[]{from};
        checkMailAddress(fromArray);
        checkMailAddress(toList);
        checkMailAddress(ccList);
        checkMailAddress(bccList);
        try {
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom(fromArray[0], fromName);
            helper.setSubject(subject);
            helper.setText(content, true);
            helper.setTo(toList);
            helper.setReplyTo(from);
            if (ccList != null) {
                helper.setCc(ccList);
            }
            if (bccList != null) {
                helper.setBcc(bccList);
            }
            /** 使用addInline 的 cid:contentId(<img alt="Capvision Logo" src="cid:logo.png">) 添加 html 元素中的src属性中对应的文件, 这样加载图片，会比直接使用url的图片，直接显示url的图片在邮件不会直接显示，会不安全 **/
            if (content.contains("src=\"cid:logo.png\"")) {
                helper.addInline("logo.png", new ClassPathResource("img/logo.png"));
            }

            return helper;
        } catch (Exception e) {
            checkException(e);
        }

        return null;
    }

    /**
     * 判断是否是不需要重发邮件的异常
     */
    private static void checkException(Exception e) throws Exception {
        String exceptionInfo = ExceptionUtil.getExceptionInfo(e);
        String regex = "(NullPointerException)|(IllegalArgumentException)|(AddressException)";
        Pattern pattern = Pattern.compile(regex, Pattern.MULTILINE);
        Matcher matcher = pattern.matcher(exceptionInfo);
        if (matcher.find()) {
            throw new MqMailException(exceptionInfo);
        }

        throw e;
    }

    /**
     * 发送邮件
     */
    private static void sendMail(MimeMessage message) throws Exception {
        try {
            sender.setUsername(getSender());
            sender.send(message);
        } catch (MailSendException e) {
            // 如果抛出连接邮件服务器超时的异常，则mq 的ack确认消息返回一个未处理成功的unack确认消息，用于重新处理该消息
            /*String regex = "(Could not connect to SMTP host)|(Could not convert socket to TLS)|(java\\.net\\.SocketTimeoutException: Read timed out)|(javax.mail.MessagingException: \\[EOF\\])|(Can't send command to SMTP host)";
            Pattern pattern = Pattern.compile(regex, Pattern.MULTILINE);
            Matcher matcher = pattern.matcher(exceptionInfo);
            if (matcher.find()) {
                throw new MqMailException(exceptionInfo);
            }*/

            checkException(e);
        }
    }

    private static void buildEmailAtt(EmailAttModel eam, MimeMessageHelper helper) throws Exception {
        if (eam.getAtt() != null && eam.getAtt().length > 0) {
            helper.addAttachment(eam.getAttName(), new ByteArrayResource(eam.getAtt(), eam.getAttType()));
            return;
        }

        if (!StringUtils.isEmpty(eam.getFilePath())) {
            EmailAttModel emailAttModel = build_att_by_file_path(eam.getFilePath(), eam.getAttName());

            helper.addAttachment(emailAttModel.getAttName(),
                    new ByteArrayResource(emailAttModel.getAtt(), emailAttModel.getAttType()),
                    emailAttModel.getAttType());
        }
    }

    public static EmailAttModel build_att_by_file_path(String file_path, String att_name) throws UnirestException, IOException {
        HttpResponse<InputStream> response = Unirest.get(file_path).asBinary();
        EmailAttModel eam = new EmailAttModel();

        String fileName;
        String raw = response.getHeaders().getFirst("Content-Disposition");
        // raw = "attachment; filename=abc.jpg"
        if (raw != null && raw.indexOf('=') != -1 && StringUtils.isEmpty(att_name)) {
            fileName = raw.replaceFirst("(?i)^.*filename=\"?([^\"]+)\"?.*$", "$1");
        } else {
            fileName = att_name;
        }

        String fileType = response.getHeaders().getFirst("Content-Type");
        if (StringUtils.isEmpty(fileType)) {
            fileType = eam.getAttType();
        }

        eam.setAttType(fileType);
        eam.setAttName(fileName);
        eam.setAtt(IOUtils.toByteArray(response.getRawBody()));

        return eam;
    }



    private static Attendee createEventAttendee(String participant, String name) {
        Attendee attendee = new Attendee(URI.create("mailto:"
                + participant));
        attendee.getParameters().add(Role.REQ_PARTICIPANT);
        attendee.getParameters().add(PartStat.NEEDS_ACTION);
        attendee.getParameters().add(Rsvp.TRUE);
        attendee.getParameters().add(new Cn(name));

        return attendee;
    }

    private static void setCalendarXProperty(VEvent meeting, int sequence, boolean isCancelCalendar) {
        int priority = 5;
        String status = "CONFIRMED";
        String importance = "1";
        /** 如过是取消日历，则设置这些属性值 **/
        if (isCancelCalendar) {
            priority = 1;
            status = "CANCELLED";
            importance = "2";
        }

        meeting.getProperties().add(new Clazz("PUBLIC"));
        meeting.getProperties().add(new Priority(priority));
        meeting.getProperties().add(new Transp("OPAQUE"));
        meeting.getProperties().add(new Status(status));

        meeting.getProperties().add(new XProperty("X-MICROSOFT-CDO-APPT-SEQUENCE", String.valueOf(sequence)));
        meeting.getProperties().add(new XProperty("X-MICROSOFT-CDO-OWNERAPPTID", "-1399179299"));
        meeting.getProperties().add(new XProperty("X-MICROSOFT-CDO-BUSYSTATUS", "TENTATIVE"));
        meeting.getProperties().add(new XProperty("X-MICROSOFT-CDO-INTENDEDSTATUS", "BUSY"));
        meeting.getProperties().add(new XProperty("X-MICROSOFT-CDO-ALLDAYEVENT", "FALSE"));
        meeting.getProperties().add(new XProperty("X-MICROSOFT-CDO-IMPORTANCE", importance));
        meeting.getProperties().add(new XProperty("X-MICROSOFT-CDO-INSTTYPE", "0"));
        meeting.getProperties().add(new XProperty("X-MICROSOFT-DISALLOW-COUNTER", "FALSE"));

    }

    private static List<Attendee> createAttendeeList(List<String> reqs, VEvent meeting) {
        List<Attendee> list = new ArrayList<>();
        for (String participant : reqs) {
            Attendee attendee = createEventAttendee(participant, participant);

            meeting.getProperties().add(attendee);
            list.add(attendee);
        }

        return list;
    }

    public static void main(String[] args) throws Exception {
        /*File file = new File("C:\\Users\\<USER>\\Pictures\\Backend_D.png");
        ExchangeSender.sendWithAtt(
                "<EMAIL>",
                new String[] {"<EMAIL>"},
                new String[] {"<EMAIL>"},
                new String[] {"<EMAIL>"},
                "hi", "haha", "aa",
                Files.probeContentType(Paths.get(file.getPath())),
                IOUtils.toByteArray(new FileInputStream(file))
                );*/
        List<String> reqs = new ArrayList<>();
        /*reqs.add("<EMAIL>");*/
        reqs.add("<EMAIL>");
        Calendar calendar = Calendar.getInstance();
        calendar.set(2016, Calendar.NOVEMBER, 15, 14, 30);
        Date endDate = new Date(calendar.getTime().getTime() + 60 * 1000);

        ExchangeSender.sendEventEmail("cap_2987665821981", 2, "<EMAIL>", "pingli", reqs,
                new ArrayList<String>(),
                calendar.getTime(),
                endDate, "ShangHai", "凯盛日历测试", "<strong>顾问编号:</strong> LEO<br />");
        reqs.clear();
        reqs.add("<EMAIL>");
        ExchangeSender.sendEventEmail("cap_2987665821981", 2, MailConst.SYSTEM_EMAIL, MailConst.SYSTEM_EMAIL_NAME, reqs,
                new ArrayList<String>(),
                calendar.getTime(),
                endDate, "ShangHai", "凯盛日历测试", "<strong>顾问编号:</strong> LEO<br />");

        ExchangeSender.cancelEventEmail("cap_2987665821981", 3, "<EMAIL>", "cosx", reqs,
                new ArrayList<String>(),
                calendar.getTime(),
                endDate, "ShangHai", "凯盛日历测试", "<strong>顾问编号:</strong> LEO<br />");
        reqs.clear();
        reqs.add("<EMAIL>");
        ExchangeSender.cancelEventEmail("cap_2987665821981", 3, MailConst.SYSTEM_EMAIL, MailConst.SYSTEM_EMAIL_NAME, reqs,
                new ArrayList<String>(),
                calendar.getTime(),
                endDate, "ShangHai", "凯盛日历测试", "<strong>顾问编号:</strong> LEO<br />");

        /*File file = new File("C:\\Users\\<USER>\\Pictures\\images.jpg");

        EmailAttModel eam = new EmailAttModel();
        eam.setAtt(IOUtils.toByteArray(new FileInputStream(file)));
        eam.setAttType(Files.probeContentType(Paths.get(file.getPath())));
        eam.setAttName(file.getName());

        EmailAttModel eam1 = new EmailAttModel();
        eam1.setAttName("abc.pdf");
        eam1.setFilePath("http://qa.ndb.capvision.com/files/fileUpload/2016/08/19/20160819-f199d119-685a-4e09-b5e7-8546905b8895.pdf");

        EmailAttModel eam2 = new EmailAttModel();
        eam2.setAttName("abc.pdf");
        eam2.setFilePath("http://qa.ndb.capvision.com/files/fileUpload/2016/08/19/20160819-f199d119-685a-4e09-b5e7-8546905b8895.pdf");


        ExchangeSender.sendWithAtt(
                "<EMAIL>", "cosx",
                new String[] {"<EMAIL>"},
                new String[] {"<EMAIL>"},
                new String[] {"<EMAIL>"},
                "hi", "haha", Arrays.asList(eam, eam1, eam2));*/
    }
}
