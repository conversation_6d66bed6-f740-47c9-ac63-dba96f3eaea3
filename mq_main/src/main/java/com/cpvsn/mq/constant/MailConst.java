package com.cpvsn.mq.constant;

import com.cpvsn.mq.loader.ConfigLoader;

/**
 * Created by cosx on 2016/9/2.
 */
public class MailConst {

    public static final String TYPE_SEND_CLOUD = "000";
    public static final String TYPE_263 = "001";
    public static final String TYPE_EXCHANGE = "002";
    public static final String TYPE_WITH_ATT = "003";

    public static final String SYSTEM_EMAIL = ConfigLoader.getMainConfig().proxy_send_email_default();
    public static final String SYSTEM_EMAIL_NAME = "DB Sender";
    public static final String SYSTEM_CALENDAR_EMAIL = "<EMAIL>";
    public static final String SYSTEM_CALENDAR_EMAIL_NAME = "Capvision Meetings";

    public static final int SENDER_TYPE_COMMON = 0;
    public static final int SENDER_TYPE_INNER_ALERT = 1;

    public static final int EMAIL_TO = 0;
    public static final int EMAIL_CC = 1;
    public static final int EMAIL_BCC = 2;

    public static final String DB_SENDER_MAIL = "<EMAIL>";


    public static final String MAIL_SEND_TYPE_EXCHANGE = "exchange";
    public static final String MAIL_SEND_TYPE_GRAPH_API = "microsoft_graph_api";

    public static final String CAPVISION_PRO_GMAIL_SUFFIX = "@capvision-pro.com";
}
