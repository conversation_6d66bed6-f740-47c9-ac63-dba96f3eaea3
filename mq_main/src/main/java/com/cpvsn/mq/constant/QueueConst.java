package com.cpvsn.mq.constant;

import cn.jpush.api.utils.StringUtils;
import com.cpvsn.mq.loader.ConfigLoader;

public class QueueConst {

    public static final String QUEUE_MAIL = "to_mail";
    public static final String QUEUE_MAIL_RATE = "mail";
    public static final String QUEUE_MAIL_OUTREACH = "to_mail_outreach";
    public static final String QUEUE_MAIL_OUTREACH_RATE = "mail_outreach";
    public static final String QUEUE_MAIL_BATCH = "mail_batch";

    public static String build_route_key(String queue) {
        if ("mail_k8s".equals(queue)) {
            return queue;
        }
        return StringUtils.isEmpty(ConfigLoader.getMainConfig().mq_route_key_suffix())
                ? queue : queue + "_" + ConfigLoader.getMainConfig().mq_route_key_suffix();
    }

}
