package com.cpvsn.mq.config;

import org.aeonbits.owner.Config;

/**
 * Created by cosx on 2015/10/18.
 */
@Config.Sources({
        "classpath:properties/conf.properties"
})
public interface MainConfig extends Config {

    String ndb_host();

    String intent_receive_link();

    String intent_receive_link_wap();

    String client_email_confirm_link();

    String app_key();
    String master_secret();

    String ntp_mail_group();

    int delay_msg_delta();

    String client_profile_url();

    String client_contact_profile_url();

    String app_id();
    String secret_key();
    String voice_local_file();

    String word_gen_url();

    String sms_persist_url();

    String sendgrid_api_key();
    String postmark_api_token();

    String proxy_send_email_default();
    String proxy_send_email_list();
    String proxy_send_email_password();
    String mq_route_key_suffix();
    String outreach_mail_send_type();
    int outreach_mail_quota();
    String mail_send_type();
    String mail_send_default_vendor();

    String resend_api_key();
    String mailgun_api_key();

    String mailjet_api_key();
    String mailjet_api_secret();
}
