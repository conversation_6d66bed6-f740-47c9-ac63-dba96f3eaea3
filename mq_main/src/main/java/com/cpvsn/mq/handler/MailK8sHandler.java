package com.cpvsn.mq.handler;

import com.cpvsn.core.util.LogUtil;
import com.cpvsn.mq.common.annotation.HandlerType;
import com.cpvsn.mq.util.MqUtil;
import com.cpvsn.mq.listener.AcknowledgeMqListener;
import org.slf4j.Logger;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.stereotype.Component;


@Component
@HandlerType(routing_key = "mail_k8s", concurrent_consumers = 1, ack_mode = AcknowledgeMode.MANUAL)
public class MailK8sHandler extends AcknowledgeMqListener  {
    private Logger log = LogUtil.getLog(MailK8sHandler.class);

    @Override
    public void handleMessage(String msg) throws Exception {
        MqUtil.sendMailMq(msg);
    }
}
