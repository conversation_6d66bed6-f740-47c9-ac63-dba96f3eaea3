package com.cpvsn.mq.handler;

import com.alibaba.fastjson.JSONObject;
import com.cpvsn.core.util.LogUtil;
import com.cpvsn.mq.common.annotation.HandlerType;
import com.cpvsn.mq.common.model.email.BatchEmailModel;
import com.cpvsn.mq.constant.QueueConst;
import com.cpvsn.mq.listener.AcknowledgeMqListener;
import com.cpvsn.mq.service.MailService;
import com.cpvsn.mq.service.impl.ResendService;
import org.slf4j.Logger;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@HandlerType(routing_key = QueueConst.QUEUE_MAIL_BATCH, concurrent_consumers = 4, ack_mode = AcknowledgeMode.MANUAL)
public class MailBatchHandler extends AcknowledgeMqListener {

    private Logger log = LogUtil.getLog(MailBatchHandler.class);

    @Autowired
    private MailService mailService;
    @Autowired
    private ResendService resendService;


    @Override
    protected void handleMessage(String msg) throws Exception {
        JSONObject jsonObj = JSONObject.parseObject(msg);
        String msgType = jsonObj.getString("msg_type");
        JSONObject dataObj = jsonObj.getJSONObject("data");

        log.info("[Mail Batch] send {} {}", msgType, dataObj.toJSONString());

        mailService.sendBatchMail(JSONObject.parseObject(dataObj.toJSONString(), BatchEmailModel.class));

        log.info("[Mail Batch] send {} {}", msgType, "ok");

    }
}
