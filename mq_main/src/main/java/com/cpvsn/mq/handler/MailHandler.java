package com.cpvsn.mq.handler;

import com.alibaba.fastjson.JSONObject;
import com.cpvsn.core.util.LogUtil;
import com.cpvsn.mq.common.annotation.HandlerType;
import com.cpvsn.mq.common.model.email.BatchEmailModel;
import com.cpvsn.mq.config.MainConfig;
import com.cpvsn.mq.constant.MailConst;
import com.cpvsn.mq.constant.QueueConst;
import com.cpvsn.mq.listener.AcknowledgeMqListener;
import com.cpvsn.mq.common.model.email.EmailModel;
import com.cpvsn.mq.loader.ConfigLoader;
import com.cpvsn.mq.mail.ExchangeSender;
import com.cpvsn.mq.mail.MicrosoftGraphApiSender;
import com.cpvsn.mq.service.MailService;
import com.cpvsn.mq.service.impl.MailServiceImpl;
import com.cpvsn.mq.service.impl.PostMarkService;
import com.cpvsn.mq.service.impl.SendgridMailService;
import com.cpvsn.mq.util.StringUtil;
import org.aeonbits.owner.ConfigFactory;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by cosx on 2015/10/18.
 */
@Component
@HandlerType(routing_key = QueueConst.QUEUE_MAIL, concurrent_consumers = 4, ack_mode = AcknowledgeMode.MANUAL)
public class MailHandler extends AcknowledgeMqListener {

    private Logger log = LogUtil.getLog(MailHandler.class);
    private final static MainConfig mainConfig = ConfigFactory.create(MainConfig.class);
    @Autowired
    private MailService mailService;
    @Autowired
    private SendgridMailService sendgridMailService;
    @Autowired
    private PostMarkService postMarkService;

    @Override
    public void handleMessage(String msg) throws Exception {
        JSONObject jsonObj = JSONObject.parseObject(msg);
        String msgType = jsonObj.getString("msg_type");
        JSONObject dataObj = jsonObj.getJSONObject("data");

        log.info("[Mail] send {} {}", msgType, dataObj.toJSONString());
        switch (msgType) {
            case MailServiceImpl.MSG_TYP_MAIL:
                EmailModel model = JSONObject.parseObject(dataObj.toJSONString(), EmailModel.class);
                mailService.sendMail(model);
                break;
            case MailServiceImpl.SEND_CALENDAR:
                mailService.sendCalendar(JSONObject.parseObject(dataObj.toJSONString(), EmailModel.class));
                break;
            case MailServiceImpl.CANCEL_CALENDAR:
                mailService.cancelCalendar(JSONObject.parseObject(dataObj.toJSONString(), EmailModel.class));
                break;
            case MailServiceImpl.BATCH_EXCHANGE:
                sendMailViaExchange(dataObj);
                break;
        }

        log.info("[Mail] send {} {}", msgType, "ok");
    }

    private void sendMailViaExchange(JSONObject dataObj) {
        EmailModel emailModel = JSONObject.parseObject(dataObj.toJSONString(), EmailModel.class);

        log.info("[Mail] send exchange email {}", emailModel.toString());

        try {
            if (MailConst.MAIL_SEND_TYPE_GRAPH_API.equals(mainConfig.mail_send_type())) {
                mailService.filterEmailModel(emailModel);
                MicrosoftGraphApiSender.sendEmailWithAttachments(emailModel);
            } else {
                ExchangeSender.sendWithAtt(MailConst.SENDER_TYPE_COMMON, emailModel.getFrom(),
                        emailModel.getFromName(),
                        emailModel.getTo().toArray(new String[emailModel.getTo().size()]),
                        emailModel.getCc().toArray(new String[emailModel.getCc().size()]),
                        emailModel.getBcc().toArray(new String[emailModel.getBcc().size()]),
                        emailModel.getSubject(), emailModel.getContent(),
                        emailModel.getAttList(), emailModel
                );
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("[Exchange]", e);
        }

        log.info("[Mail] send exchange email success");
    }

    /**
     * 清除邮件地址中的空白字符
     */
    private void clearEmailAddress(EmailModel emailModel) {
        emailModel.setTo(removeListTrim(emailModel.getTo()));
        emailModel.setCc(removeListTrim(emailModel.getCc()));
        emailModel.setBcc(removeListTrim(emailModel.getBcc()));
    }

    private List<String> removeListTrim(List<String> strList) {
        List<String> newList = new ArrayList<>();
        if (strList == null || strList.size() == 0) {
            return strList;
        }
        for (String str : strList) {
            newList.add(StringUtil.removeTrim(str));
        }

        return newList;
    }

}
