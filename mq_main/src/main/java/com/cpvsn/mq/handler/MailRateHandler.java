package com.cpvsn.mq.handler;

import com.cpvsn.core.util.LogUtil;
import com.cpvsn.mq.common.annotation.HandlerType;
import com.cpvsn.mq.util.MqUtil;
import com.cpvsn.mq.constant.QueueConst;
import com.cpvsn.mq.listener.AcknowledgeMqListener;
import org.slf4j.Logger;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.stereotype.Component;

/**
 * Created by pingli on 2020/11/17.
 */
@Component
@HandlerType(routing_key = QueueConst.QUEUE_MAIL_RATE, concurrent_consumers = 1, ack_mode = AcknowledgeMode.MANUAL)
public class MailRateHandler extends AcknowledgeMqListener {

	private Logger log = LogUtil.getLog(MailRateHandler.class);

	@Override
	public void handleMessage(String msg) throws Exception {
		long startTime = System.currentTimeMillis();
		transMessage(msg);
		long costTime = System.currentTimeMillis() - startTime;
		if (1000 - costTime > 0) {
			Thread.sleep(1000 - costTime);
		}
	}

	private void transMessage(String msg) {
		MqUtil.sendToMailMq(msg);
	}

}

