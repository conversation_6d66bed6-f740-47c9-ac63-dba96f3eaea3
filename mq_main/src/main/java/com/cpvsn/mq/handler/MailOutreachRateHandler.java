package com.cpvsn.mq.handler;

import com.cpvsn.core.factory.CoreFactory;
import com.cpvsn.core.service.RedisService;
import com.cpvsn.core.util.LogUtil;
import com.cpvsn.mq.common.annotation.HandlerType;
import com.cpvsn.mq.common.model.OutreachMailCount;
import com.cpvsn.mq.constant.QueueConst;
import com.cpvsn.mq.constant.RedisConst;
import com.cpvsn.mq.listener.AcknowledgeMqListener;
import com.cpvsn.mq.loader.ConfigLoader;
import com.cpvsn.mq.util.DateUtil;
import com.cpvsn.mq.util.MqUtil;
import org.slf4j.Logger;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 控制mail outreach每十分钟发送邮件的数量
 * Created by pingli on 2022/8/1.
 */
@Component
@HandlerType(routing_key = QueueConst.QUEUE_MAIL_OUTREACH_RATE, concurrent_consumers = 1, ack_mode = AcknowledgeMode.MANUAL)
public class MailOutreachRateHandler extends AcknowledgeMqListener {

	private Logger log = LogUtil.getLog(MailOutreachRateHandler.class);
	private final  RedisService redisService = CoreFactory.getRedis();

	@Override
	protected void handleMessage(String message) throws Exception {
		OutreachMailCount count = get_outreach_mail_count();
		/** 十分钟内发送的outreach邮件数量超额了，则放到下一个10分钟再发送 **/
		while (count.getCount() > ConfigLoader.getMainConfig().outreach_mail_quota()) {
			Thread.sleep(60000);
			count = get_outreach_mail_count();
		}

		MqUtil.sendToMailOutreachMq(message);

		redisService.set(count.getKey(), count.getCount() + 1);
		redisService.expire(count.getKey(), 900);
	}

	/**
	 * 获得十分钟内发送的outreach邮件数量
	 * @return
	 */
	private OutreachMailCount get_outreach_mail_count() {
		String key = RedisConst.OUTREACH_MAIL_COUNT_KEY
				+ DateUtil.date_str(LocalDateTime.now(), DateUtil.SDF_SHORT_HM).substring(0, 11);

		return new OutreachMailCount(Optional.ofNullable(redisService.get(key, Integer.class)).orElse(0), key);
	}



}
