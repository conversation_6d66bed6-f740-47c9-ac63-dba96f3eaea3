package com.cpvsn.mq.handler;

import com.alibaba.fastjson.JSONObject;
import com.cpvsn.core.util.LogUtil;
import com.cpvsn.mq.common.annotation.HandlerType;
import com.cpvsn.mq.common.model.email.EmailModel;
import com.cpvsn.mq.constant.QueueConst;
import com.cpvsn.mq.listener.AcknowledgeMqListener;
import com.cpvsn.mq.loader.ConfigLoader;
import com.cpvsn.mq.service.MailService;
import com.cpvsn.mq.service.impl.MailServiceImpl;
import com.cpvsn.mq.service.impl.PostMarkService;
import com.cpvsn.mq.service.impl.SendgridMailService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by pingli on 2021/11/4.
 * New US DB leads 的outreach邮件使用单独队列处理发送
 *
 */
@Component
@HandlerType(routing_key = QueueConst.QUEUE_MAIL_OUTREACH, concurrent_consumers = 4, ack_mode = AcknowledgeMode.MANUAL)
public class MailOutreachHandler extends AcknowledgeMqListener {

	private Logger log = LogUtil.getLog(MailOutreachHandler.class);

	@Autowired
	private MailService mailService;
	@Autowired
	private SendgridMailService sendgridMailService;
	@Autowired
	private PostMarkService postMarkService;

	@Override
	protected void handleMessage(String msg) throws Exception {
		JSONObject jsonObj = JSONObject.parseObject(msg);
		String msgType = jsonObj.getString("msg_type");
		JSONObject dataObj = jsonObj.getJSONObject("data");

		log.info("[Mail Outreach] send {} {}", msgType, dataObj.toJSONString());
		EmailModel model = JSONObject.parseObject(dataObj.toJSONString(), EmailModel.class);
		switch (msgType) {
			case MailServiceImpl.MSG_TYP_MAIL:
				mailService.sendMail(model);
				break;
			case MailServiceImpl.SEND_CALENDAR:
				mailService.sendCalendar(model);
				break;
			case MailServiceImpl.CANCEL_CALENDAR:
				mailService.cancelCalendar(model);
				break;
		}

		log.info("[Mail Outreach] send {} {}", msgType, "ok");
	}
}
