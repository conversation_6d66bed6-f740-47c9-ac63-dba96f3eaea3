workflow:
  rules:
    - if: $CI_COMMIT_BRANCH =~ /^master.*$/ || $CI_COMMIT_BRANCH =~ /^preview.*$/
      when: always
    - when: never

cache: &global_cache
  key: ${CI_PROJECT_NAME}
  paths:
    - gradle_cache
  policy: pull-push

stages:
  - build


deploy_dev:
  extends: .build_scripts
  except:
    - master
  environment:
    name: aws/${CI_COMMIT_BRANCH}
deploy_master:
  extends: .build_scripts
  only:
    - master
  environment:
    name: aws/${CI_COMMIT_BRANCH}


.build_scripts:
  tags:
    - k8s
  stage: build
  image:
    name: 133841643524.dkr.ecr.us-east-2.amazonaws.com/gradle_kaniko_kubectl:v1.2
  before_script:
    - export NAMESPACE=default
    - |
      if [[ "$CI_COMMIT_BRANCH" =~ .*sg$ ]]
      then
          export REGION=sg
          export NAMESPACE=sg
          export TASK_NAME=releaseProdSg
      else
          export REGION=us
          export NAMESPACE=default
          export TASK_NAME=releaseProdUs
      fi
    - |
      if [[ "$CI_COMMIT_BRANCH" =~ ^master.*$ ]]
      then
          :
      else 
          export TASK_NAME=releaseTest
          export NAMESPACE=dev
      fi
    - export REPLICAS=1
  script:
    - /kaniko/kubectl get secrets/default-aws-ecr-us-east-2  -n gitlab -o jsonpath='{.data.\.dockerconfigjson}' | base64 -d > /kaniko/.docker/config.json
    - chmod 755 gradlew
    - ./gradlew -p mq_main ${TASK_NAME} -x test --stacktrace -Dgradle.user.home=./gradle_cache
    - |
      /kaniko/executor \
        --context "${CI_PROJECT_DIR}/mq_main/dist" \
        --build-arg BUILD_ENV=${CI_COMMIT_BRANCH} \
        --dockerfile "${CI_PROJECT_DIR}/mq_main/Dockerfile" \
        --destination "133841643524.dkr.ecr.us-east-2.amazonaws.com/${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH}:${CI_COMMIT_SHORT_SHA}"

    - cd ${CI_PROJECT_DIR}/manifests/
    - export LC_ALL=C
    - /kaniko/envsubst < deployment.tpl.yaml > deployment.yaml
    - /kaniko/kubectl apply -f deployment.yaml
  cache:
    <<: *global_cache
