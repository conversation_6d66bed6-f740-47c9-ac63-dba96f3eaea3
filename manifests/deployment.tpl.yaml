apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH}
  namespace: ${NAMESPACE}
spec:
  replicas: ${REPLICAS}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: ${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH}
  template:
    metadata:
      labels:
        app: ${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH}
    spec:
      containers:
        - name: ${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH}
          image: 133841643524.dkr.ecr.us-east-2.amazonaws.com/${CI_PROJECT_NAME}-${CI_COMMIT_BRANCH}:${CI_COMMIT_SHORT_SHA}
          env:
            - name: BRANCH
              value: ${CI_COMMIT_BRANCH}
            - name: ENV
              value: ${ENV}
          ports:
            - containerPort: 8002
      imagePullSecrets:
        - name: default-aws-ecr-us-east-2


