apiVersion: apps/v1
kind: Deployment
metadata:
  name: <JOB_NAME>-<BRANCH>
  namespace: <NAMESPACE>
spec:
  replicas: <REPLICAS>
  selector:
    matchLabels:
      app: <JOB_NAME>-<BRANCH>
  template:
    metadata:
      labels:
        app: <JOB_NAME>-<BRANCH>
    spec:
      containers:
      - name: <JOB_NAME>-<BRANCH>
        image: 133841643524.dkr.ecr.us-east-2.amazonaws.com/<JOB_NAME>:<BRANCH>-<BUILD_NUMBER>
        ports:
        - containerPort: 8002
      imagePullSecrets:
      - name: default-aws-ecr-us-east-2
